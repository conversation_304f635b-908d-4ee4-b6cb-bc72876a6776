/**
 * BookInn Rate Plans Stylesheet
 * 
 * Independent styling for rate plans management
 * Follows BookInn design system patterns
 */

/* Rate Plans Container */
.bookinn-rate-plans-container {
    background: #fff;
    border-radius: var(--bookinn-radius-lg, 8px);
    box-shadow: var(--bookinn-shadow, 0 1px 3px rgba(0, 0, 0, 0.1));
    overflow: hidden;
}

/* Rate Plans Header */
.bookinn-rate-plans-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--bookinn-space-4, 1rem) var(--bookinn-space-6, 1.5rem);
    border-bottom: 1px solid var(--bookinn-border, #e5e7eb);
    background: var(--bookinn-bg-light, #f9fafb);
}

.bookinn-rate-plans-header h3 {
    margin: 0;
    font-size: var(--bookinn-text-lg, 1.125rem);
    font-weight: 600;
    color: var(--bookinn-text-dark, #111827);
}

/* Rate Plans Actions */
.bookinn-rate-plans-actions {
    display: flex;
    gap: var(--bookinn-space-2, 0.5rem);
    align-items: center;
}

/* Rate Plans Filters */
.bookinn-rate-plans-filters {
    padding: var(--bookinn-space-4, 1rem) var(--bookinn-space-6, 1.5rem);
    background: var(--bookinn-bg-light, #f9fafb);
    border-bottom: 1px solid var(--bookinn-border, #e5e7eb);
}

.bookinn-rate-plans-filters.hidden {
    display: none;
}

.bookinn-filters-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--bookinn-space-4, 1rem);
    margin-bottom: var(--bookinn-space-4, 1rem);
}

.bookinn-filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--bookinn-space-1, 0.25rem);
}

.bookinn-filter-group label {
    font-size: var(--bookinn-text-sm, 0.875rem);
    font-weight: 500;
    color: var(--bookinn-text-dark, #111827);
}

.bookinn-filters-actions {
    display: flex;
    gap: var(--bookinn-space-2, 0.5rem);
    justify-content: flex-end;
}

/* Rate Plans Table */
.bookinn-rate-plans-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--bookinn-text-sm, 0.875rem);
}

.bookinn-rate-plans-table th {
    background: var(--bookinn-bg-light, #f9fafb);
    padding: var(--bookinn-space-3, 0.75rem) var(--bookinn-space-4, 1rem);
    text-align: left;
    font-weight: 600;
    color: var(--bookinn-text-dark, #111827);
    border-bottom: 1px solid var(--bookinn-border, #e5e7eb);
    white-space: nowrap;
}

.bookinn-rate-plans-table td {
    padding: var(--bookinn-space-3, 0.75rem) var(--bookinn-space-4, 1rem);
    border-bottom: 1px solid var(--bookinn-border, #e5e7eb);
    vertical-align: top;
}

.bookinn-rate-plans-table tr:hover {
    background: var(--bookinn-bg-hover, #f3f4f6);
}

/* Rate Plan Row States */
.bookinn-rate-plans-table tr[data-status="inactive"] {
    opacity: 0.6;
}

.bookinn-rate-plans-table tr.selected {
    background: var(--bookinn-primary-light, #dbeafe);
}

/* Rate Plan Badges */
.bookinn-rate-plan-badge {
    display: inline-block;
    padding: var(--bookinn-space-1, 0.25rem) var(--bookinn-space-2, 0.5rem);
    font-size: var(--bookinn-text-xs, 0.75rem);
    font-weight: 500;
    border-radius: var(--bookinn-radius-sm, 4px);
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.bookinn-rate-plan-badge.weekend {
    background: var(--bookinn-info-light, #dbeafe);
    color: var(--bookinn-info-dark, #1e40af);
}

.bookinn-rate-plan-badge.holiday {
    background: var(--bookinn-warning-light, #fef3c7);
    color: var(--bookinn-warning-dark, #92400e);
}

/* Rate Plan Status */
.bookinn-rate-plan-status {
    display: inline-flex;
    align-items: center;
    gap: var(--bookinn-space-1, 0.25rem);
    padding: var(--bookinn-space-1, 0.25rem) var(--bookinn-space-2, 0.5rem);
    font-size: var(--bookinn-text-xs, 0.75rem);
    font-weight: 500;
    border-radius: var(--bookinn-radius-sm, 4px);
}

.bookinn-rate-plan-status.active {
    background: var(--bookinn-success-light, #d1fae5);
    color: var(--bookinn-success-dark, #065f46);
}

.bookinn-rate-plan-status.inactive {
    background: var(--bookinn-error-light, #fee2e2);
    color: var(--bookinn-error-dark, #991b1b);
}

.bookinn-rate-plan-status::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
}

/* Rate Plan Actions */
.bookinn-rate-plan-actions {
    display: flex;
    gap: var(--bookinn-space-1, 0.25rem);
    align-items: center;
}

.bookinn-rate-plan-actions .bookinn-btn {
    padding: var(--bookinn-space-1, 0.25rem) var(--bookinn-space-2, 0.5rem);
    font-size: var(--bookinn-text-xs, 0.75rem);
    min-height: auto;
}

/* Rate Plan Modal Enhancements
    NOTE: modal-specific form-section and heading rules consolidated
    in the "Modal overrides" block near the end of this file to avoid
    duplicate selectors. See the consolidated overrides around the
    "Override: force the extended two-column layout for the Rate Plan modal"
    comment for modal-specific layout and form-section settings.
*/

/* Rate Plan Form Grid */
.bookinn-rate-plan-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--bookinn-space-4, 1rem);
}

.bookinn-rate-plan-form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--bookinn-space-4, 1rem);
}

/* Rate Plan Pricing Display */
.bookinn-rate-plan-price {
    font-weight: 600;
    color: var(--bookinn-success, #10b981);
    font-size: var(--bookinn-text-base, 1rem);
}

.bookinn-rate-plan-price-large {
    font-size: var(--bookinn-text-lg, 1.125rem);
}

/* Rate Plan Date Display */
.bookinn-rate-plan-date {
    font-weight: 500;
    color: var(--bookinn-text-dark, #111827);
}

/* Rate Plan Description */
.bookinn-rate-plan-description {
    color: var(--bookinn-text-muted, #6b7280);
    font-size: var(--bookinn-text-sm, 0.875rem);
    line-height: 1.4;
}

/* Bulk Actions */
.bookinn-rate-plans-bulk-actions {
    display: none;
    padding: var(--bookinn-space-3, 0.75rem) var(--bookinn-space-6, 1.5rem);
    background: var(--bookinn-primary-light, #dbeafe);
    border-bottom: 1px solid var(--bookinn-border, #e5e7eb);
    align-items: center;
    gap: var(--bookinn-space-4, 1rem);
}

.bookinn-rate-plans-bulk-actions.show {
    display: flex;
}

.bookinn-bulk-actions-info {
    font-size: var(--bookinn-text-sm, 0.875rem);
    color: var(--bookinn-text-dark, #111827);
    font-weight: 500;
}

.bookinn-bulk-actions-buttons {
    display: flex;
    gap: var(--bookinn-space-2, 0.5rem);
}

/* Rate Plan Statistics */
.bookinn-rate-plans-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--bookinn-space-4, 1rem);
    padding: var(--bookinn-space-4, 1rem) var(--bookinn-space-6, 1.5rem);
    background: var(--bookinn-bg-light, #f9fafb);
    border-bottom: 1px solid var(--bookinn-border, #e5e7eb);
}

.bookinn-rate-plan-stat {
    text-align: center;
}

.bookinn-rate-plan-stat-value {
    display: block;
    font-size: var(--bookinn-text-2xl, 1.5rem);
    font-weight: 700;
    color: var(--bookinn-primary, #2563eb);
}

.bookinn-rate-plan-stat-label {
    display: block;
    font-size: var(--bookinn-text-sm, 0.875rem);
    color: var(--bookinn-text-muted, #6b7280);
    margin-top: var(--bookinn-space-1, 0.25rem);
}

/* Loading States */
.bookinn-rate-plans-loading {
    text-align: center;
    padding: var(--bookinn-space-8, 2rem);
    color: var(--bookinn-text-muted, #6b7280);
}

.bookinn-rate-plans-loading i {
    font-size: var(--bookinn-text-lg, 1.125rem);
    margin-right: var(--bookinn-space-2, 0.5rem);
}

/* Empty State */
.bookinn-rate-plans-empty {
    text-align: center;
    padding: var(--bookinn-space-12, 3rem) var(--bookinn-space-6, 1.5rem);
}

.bookinn-rate-plans-empty-icon {
    font-size: 3rem;
    color: var(--bookinn-text-light, #d1d5db);
    margin-bottom: var(--bookinn-space-4, 1rem);
}

.bookinn-rate-plans-empty-title {
    font-size: var(--bookinn-text-lg, 1.125rem);
    font-weight: 600;
    color: var(--bookinn-text-dark, #111827);
    margin-bottom: var(--bookinn-space-2, 0.5rem);
}

.bookinn-rate-plans-empty-description {
    color: var(--bookinn-text-muted, #6b7280);
    margin-bottom: var(--bookinn-space-6, 1.5rem);
}

/* Responsive Design */
@media (max-width: 768px) {
    .bookinn-rate-plans-header {
        flex-direction: column;
        gap: var(--bookinn-space-4, 1rem);
        align-items: stretch;
    }
    
    .bookinn-rate-plans-actions {
        justify-content: center;
    }
    
    .bookinn-filters-row {
        grid-template-columns: 1fr;
    }
    
    
    .bookinn-rate-plans-table {
        font-size: var(--bookinn-text-xs, 0.75rem);
    }
    
    .bookinn-rate-plans-table th,
    .bookinn-rate-plans-table td {
        padding: var(--bookinn-space-2, 0.5rem);
    }
    
    .bookinn-rate-plan-actions {
        flex-direction: column;
        gap: var(--bookinn-space-1, 0.25rem);
    }
}

/* Rate Plan Modal Specific Styles */
.bookinn-modal#bookinn-rate-plan-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.5) !important;
    display: none !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 9999 !important;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    box-sizing: border-box !important;
}

.bookinn-modal#bookinn-rate-plan-modal.bookinn-modal-show,
.bookinn-modal#bookinn-rate-plan-modal[style*="display: flex"] {
    display: flex !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Large Modal for Enhanced Rate Plans
    (consolidated later in the file with other modal overrides)
*/

/* Form Grid Layouts */
.bookinn-form-grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--bookinn-space-4, 1rem);
}

.bookinn-form-grid-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--bookinn-space-4, 1rem);
}

/* Form Sections */
.bookinn-form-section {
    margin-bottom: var(--bookinn-space-6, 1.5rem);
    padding-bottom: var(--bookinn-space-4, 1rem);
    border-bottom: 1px solid var(--bookinn-border, #e5e7eb);
}

.bookinn-form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.bookinn-form-section h5 {
    margin: 0 0 var(--bookinn-space-4, 1rem) 0;
    font-size: var(--bookinn-text-base, 1rem);
    font-weight: 600;
    color: var(--bookinn-text-dark, #111827);
    padding-bottom: var(--bookinn-space-2, 0.5rem);
    border-bottom: 2px solid var(--bookinn-primary, #2563eb);
}

/* Help Text */
.bookinn-help-text {
    display: block;
    font-size: var(--bookinn-text-xs, 0.75rem);
    color: var(--bookinn-text-muted, #6b7280);
    margin-top: var(--bookinn-space-1, 0.25rem);
    line-height: 1.4;
}

/* Cancellation Policy Help Text */
.bookinn-cancellation-help {
    background: var(--bookinn-bg-light, #f9fafb);
    padding: var(--bookinn-space-3, 0.75rem);
    border-radius: var(--bookinn-radius-sm, 4px);
    border-left: 3px solid var(--bookinn-primary, #2563eb);
    margin-top: var(--bookinn-space-2, 0.5rem);
}

.bookinn-cancellation-help h6 {
    margin: 0 0 var(--bookinn-space-2, 0.5rem) 0;
    font-size: var(--bookinn-text-sm, 0.875rem);
    font-weight: 600;
    color: var(--bookinn-text-dark, #111827);
}

.bookinn-cancellation-help ul {
    margin: 0;
    padding-left: var(--bookinn-space-4, 1rem);
    list-style-type: disc;
}

.bookinn-cancellation-help li {
    margin-bottom: var(--bookinn-space-1, 0.25rem);
    font-size: var(--bookinn-text-xs, 0.75rem);
    color: var(--bookinn-text-muted, #6b7280);
    line-height: 1.4;
}

/* Custom Cancellation Field */
.bookinn-custom-cancellation-field {
    display: none;
    margin-top: var(--bookinn-space-3, 0.75rem);
    padding: var(--bookinn-space-3, 0.75rem);
    background: var(--bookinn-bg-light, #f9fafb);
    border-radius: var(--bookinn-radius-sm, 4px);
    border: 1px solid var(--bookinn-border, #e5e7eb);
}

.bookinn-custom-cancellation-field.show {
    display: block;
}

.bookinn-custom-cancellation-field label {
    display: block;
    font-size: var(--bookinn-text-sm, 0.875rem);
    font-weight: 500;
    color: var(--bookinn-text-dark, #111827);
    margin-bottom: var(--bookinn-space-2, 0.5rem);
}

.bookinn-custom-cancellation-field .bookinn-input {
    width: 100%;
    max-width: 200px;
}

.bookinn-custom-cancellation-field .bookinn-help-text {
    margin-top: var(--bookinn-space-1, 0.25rem);
}

/* Input Error State */
.bookinn-input-error {
    border-color: var(--bookinn-error, #ef4444) !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

/* Checkbox Groups */
.bookinn-checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--bookinn-space-2, 0.5rem);
    cursor: pointer;
    font-size: var(--bookinn-text-sm, 0.875rem);
    color: var(--bookinn-text-dark, #111827);
}

.bookinn-checkbox-custom {
    width: 18px;
    height: 18px;
    border: 2px solid var(--bookinn-border, #d1d5db);
    border-radius: var(--bookinn-radius-sm, 4px);
    display: inline-block;
    position: relative;
    transition: all 0.2s ease;
}

.bookinn-checkbox-label input[type="checkbox"] {
    display: none;
}

.bookinn-checkbox-label input[type="checkbox"]:checked + .bookinn-checkbox-custom {
    background: var(--bookinn-primary, #2563eb);
    border-color: var(--bookinn-primary, #2563eb);
}

.bookinn-checkbox-label input[type="checkbox"]:checked + .bookinn-checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Toggle Switch Styling */
.bookinn-toggle-label {
    display: flex;
    align-items: center;
    gap: var(--bookinn-space-2, 0.5rem);
    cursor: pointer;
    font-size: var(--bookinn-text-sm, 0.875rem);
    color: var(--bookinn-text-dark, #111827);
}

.bookinn-toggle-label input[type="checkbox"] {
    display: none;
}

.bookinn-toggle-slider {
    position: relative;
    width: 50px;
    height: 24px;
    background: var(--bookinn-border, #d1d5db);
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.bookinn-toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.bookinn-toggle-label input[type="checkbox"]:checked + .bookinn-toggle-slider {
    background: var(--bookinn-success, #10b981);
}

.bookinn-toggle-label input[type="checkbox"]:checked + .bookinn-toggle-slider::before {
    transform: translateX(26px);
}

.bookinn-toggle-text {
    font-weight: 500;
    transition: color 0.3s ease;
}

.bookinn-toggle-label input[type="checkbox"]:checked ~ .bookinn-toggle-text {
    color: var(--bookinn-success, #10b981);
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
    /* Allow normal grid collapse for general forms but preserve modal grid */
    .bookinn-form-grid-2:not(.bookinn-modal .bookinn-form-grid-2) {
        grid-template-columns: 1fr;
        gap: var(--bookinn-space-3, 0.75rem);
    }

    .bookinn-modal.bookinn-modal-large .bookinn-modal-content,
    .bookinn-modal#bookinn-rate-plan-modal .bookinn-modal-content {
        width: 95%;
        max-width: 720px;
        margin: 10px;
        max-height: 95vh;
    }

    .bookinn-modal#bookinn-rate-plan-modal .bookinn-modal-header {
        padding: var(--bookinn-space-3, 0.75rem) var(--bookinn-space-4, 1rem);
    }

    .bookinn-modal#bookinn-rate-plan-modal .bookinn-modal-body {
        padding: var(--bookinn-space-4, 1rem);
    }

    .bookinn-form-section h5 {
        font-size: var(--bookinn-text-sm, 0.875rem);
        margin-bottom: var(--bookinn-space-3, 0.75rem);
    }
}

@media (max-width: 480px) {
    .bookinn-modal#bookinn-rate-plan-modal .bookinn-modal-header h4,
    .bookinn-modal#bookinn-rate-plan-modal .bookinn-modal-header h3 {
        font-size: var(--bookinn-text-base, 1rem);
    }

    .bookinn-form-section {
        margin-bottom: var(--bookinn-space-4, 1rem);
        padding-bottom: var(--bookinn-space-3, 0.75rem);
    }

    .bookinn-toggle-slider {
        width: 44px;
        height: 22px;
    }

    .bookinn-toggle-slider::before {
        width: 18px;
        height: 18px;
    }

    .bookinn-toggle-label input[type="checkbox"]:checked + .bookinn-toggle-slider::before {
        transform: translateX(22px);
    }
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.bookinn-modal#bookinn-rate-plan-modal .bookinn-modal-header {
    padding: var(--bookinn-space-4, 1rem) var(--bookinn-space-6, 1.5rem);
    border-bottom: 1px solid var(--bookinn-border, #e5e7eb);
    background: var(--bookinn-primary, #2563eb);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.bookinn-modal#bookinn-rate-plan-modal .bookinn-modal-header h4,
.bookinn-modal#bookinn-rate-plan-modal .bookinn-modal-header h3 {
    margin: 0;
    font-size: var(--bookinn-text-lg, 1.125rem);
    font-weight: 600;
    color: white !important;
}

.bookinn-modal#bookinn-rate-plan-modal .bookinn-close-modal {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.8);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.bookinn-modal#bookinn-rate-plan-modal .bookinn-close-modal:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: scale(1.1);
}

.bookinn-modal#bookinn-rate-plan-modal .bookinn-modal-body {
    padding: var(--bookinn-space-6, 1.5rem);
}

.bookinn-modal#bookinn-rate-plan-modal .bookinn-modal-footer {
    padding: var(--bookinn-space-4, 1rem) var(--bookinn-space-6, 1.5rem);
    border-top: 1px solid var(--bookinn-border, #e5e7eb);
    display: flex;
    gap: var(--bookinn-space-3, 0.75rem);
    justify-content: flex-end;
}

/* Force show modal when needed */
.bookinn-modal#bookinn-rate-plan-modal.force-show,
.bookinn-modal#bookinn-rate-plan-modal[style*="display: flex"] {
    display: flex !important;
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 99999 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.5) !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Aggressive override for modal display */
#bookinn-rate-plan-modal[style*="display: flex"] {
    display: flex !important;
}

#bookinn-rate-plan-modal[style*="opacity: 1"] {
    opacity: 1 !important;
}

#bookinn-rate-plan-modal[style*="visibility: visible"] {
    visibility: visible !important;
}

/* Ensure modal content is properly styled when shown */
.bookinn-modal#bookinn-rate-plan-modal.bookinn-modal-show .bookinn-modal-content,
.bookinn-modal#bookinn-rate-plan-modal.force-show .bookinn-modal-content,
.bookinn-modal#bookinn-rate-plan-modal[style*="display: flex"] .bookinn-modal-content {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Consolidated modal-content styles (grouped with other modal overrides) */
.bookinn-modal.bookinn-modal-large .bookinn-modal-content,
.bookinn-modal#bookinn-rate-plan-modal .bookinn-modal-content {
    background: white;
    border-radius: var(--bookinn-radius-lg, 8px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    max-width: 720px;
    width: 95%;
    max-height: 95vh;
    overflow-y: auto;
    position: relative;
    animation: modalSlideIn 0.3s ease-out;
}

@media (max-width: 720px) {
    .bookinn-modal.bookinn-modal-large .bookinn-modal-content,
    .bookinn-modal#bookinn-rate-plan-modal .bookinn-modal-content {
        width: 95%;
        max-width: 720px;
        margin: 10px;
        max-height: 95vh;
    }
}

/* -------------------------------------------------------------------------
   Override: force the extended two-column layout for the Rate Plan modal
   Rationale: there are duplicate modal style blocks (class-based and id-based)
   and responsive rules that collapse the form to a single column. To keep the
   extended two-column layout for the add/edit Rate Plan modal, we apply a
   high-specificity override that forces two-column grids for the modal only.
   This avoids removing existing rules and is low-risk.
------------------------------------------------------------------------- */
.bookinn-modal#bookinn-rate-plan-modal .bookinn-rate-plan-form-grid,
.bookinn-modal#bookinn-rate-plan-modal .bookinn-rate-plan-form-row,
#bookinn-rate-plan-modal .bookinn-rate-plan-form-grid,
#bookinn-rate-plan-modal .bookinn-rate-plan-form-row {
    grid-template-columns: 1fr 1fr !important;
    gap: var(--bookinn-space-4, 1rem) !important;
}

/* Also ensure form-row inner grids keep two columns (higher specificity) */
.bookinn-modal#bookinn-rate-plan-modal .bookinn-form-grid-2,
#bookinn-rate-plan-modal .bookinn-form-grid-2 {
    grid-template-columns: 1fr 1fr !important;
}

/* Optional: prevent the smaller responsive breakpoint from forcing single column
   specifically for this modal by overriding the media-query rules */
@media (max-width: 500px) {
    .bookinn-modal#bookinn-rate-plan-modal .bookinn-rate-plan-form-grid,
    .bookinn-modal#bookinn-rate-plan-modal .bookinn-rate-plan-form-row {
        grid-template-columns: 1fr 1fr !important;
    }
}

/* Enhanced Table Styling */
.bookinn-cell-content {
    line-height: 1.4;
}

.bookinn-price-cell {
    text-align: right;
    line-height: 1.4;
}

.bookinn-badges-cell {
    line-height: 1.6;
}

.bookinn-status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: var(--bookinn-radius-sm, 4px);
    font-size: var(--bookinn-text-xs, 0.75rem);
    font-weight: 500;
}

.bookinn-status-badge.bookinn-status-active {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #10b981;
}

.bookinn-status-badge.bookinn-status-inactive {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #ef4444;
}

.bookinn-row-inactive {
    opacity: 0.7;
    background: #f9fafb;
}

.bookinn-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: var(--bookinn-radius-sm, 4px);
    font-size: var(--bookinn-text-xs, 0.75rem);
    font-weight: 500;
    line-height: 1.2;
    margin: 1px 2px;
}

.bookinn-badge-info {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #3b82f6;
}

.bookinn-badge-warning {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #f59e0b;
}

.bookinn-badge-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #9ca3af;
}

.bookinn-badge-success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #10b981;
}

/* Action Buttons Enhancement */
.bookinn-actions {
    display: flex;
    gap: 4px;
    justify-content: center;
}

.bookinn-btn-sm {
    padding: 4px 8px;
    font-size: var(--bookinn-text-xs, 0.75rem);
    min-width: auto;
}

/* Sub-tabs Navigation Styling */
.bookinn-sub-tabs-nav {
    display: flex;
    border-bottom: 2px solid var(--bookinn-border, #e5e7eb);
    margin-bottom: var(--bookinn-space-6, 1.5rem);
    background: var(--bookinn-bg-light, #f9fafb);
    border-radius: var(--bookinn-radius-md, 6px) var(--bookinn-radius-md, 6px) 0 0;
}

.bookinn-sub-tab-link {
    display: flex;
    align-items: center;
    gap: var(--bookinn-space-2, 0.5rem);
    padding: var(--bookinn-space-4, 1rem) var(--bookinn-space-6, 1.5rem);
    text-decoration: none;
    color: var(--bookinn-text-muted, #6b7280);
    font-weight: 500;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    position: relative;
}

.bookinn-sub-tab-link:hover {
    color: var(--bookinn-primary, #2563eb);
    background: rgba(37, 99, 235, 0.05);
}

.bookinn-sub-tab-link.active {
    color: var(--bookinn-primary, #2563eb);
    border-bottom-color: var(--bookinn-primary, #2563eb);
    background: white;
}

.bookinn-sub-tab-link .bookinn-fa {
    font-size: var(--bookinn-text-sm, 0.875rem);
}

/* Sub-tabs Content */
.bookinn-sub-tabs-content {
    position: relative;
}

.bookinn-sub-tab-panel {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.bookinn-sub-tab-panel.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Guest Registry Specific Styling */
.bookinn-guests-list {
    margin-top: var(--bookinn-space-6, 1.5rem);
}

.bookinn-guest-info {
    line-height: 1.4;
}

.bookinn-guest-info strong {
    color: var(--bookinn-text-dark, #111827);
    font-weight: 600;
}

.bookinn-guest-info .bookinn-text-muted {
    color: var(--bookinn-text-muted, #6b7280);
    font-size: var(--bookinn-text-xs, 0.75rem);
}

/* Guest Actions */
.bookinn-guests-table .bookinn-actions {
    display: flex;
    gap: 4px;
    justify-content: center;
}

.bookinn-guests-table .bookinn-btn-sm {
    padding: 6px 8px;
    font-size: var(--bookinn-text-xs, 0.75rem);
    min-width: auto;
}

/* Guest Search Filters */
#bookinn-guest-filters {
    background: var(--bookinn-bg-light, #f9fafb);
    border: 1px solid var(--bookinn-border, #e5e7eb);
    border-radius: var(--bookinn-radius-md, 6px);
    padding: var(--bookinn-space-4, 1rem);
    margin-bottom: var(--bookinn-space-4, 1rem);
}

#bookinn-guest-filters .bookinn-form-grid {
    margin-bottom: var(--bookinn-space-4, 1rem);
}

#bookinn-guest-filters .bookinn-filter-actions {
    display: flex;
    gap: var(--bookinn-space-3, 0.75rem);
    justify-content: flex-end;
}

/* Guest Modal Enhancements */
.bookinn-modal#bookinn-guest-modal .bookinn-modal-content {
    max-width: 720px;
    width: 95%;
}

.bookinn-modal#bookinn-guest-modal .bookinn-form-section {
    margin-bottom: var(--bookinn-space-6, 1.5rem);
    padding-bottom: var(--bookinn-space-4, 1rem);
    border-bottom: 1px solid var(--bookinn-border, #e5e7eb);
}

.bookinn-modal#bookinn-guest-modal .bookinn-form-section:last-of-type {
    border-bottom: none;
    margin-bottom: 0;
}

.bookinn-modal#bookinn-guest-modal .bookinn-form-section h5 {
    margin: 0 0 var(--bookinn-space-4, 1rem) 0;
    font-size: var(--bookinn-text-base, 1rem);
    font-weight: 600;
    color: var(--bookinn-text-dark, #111827);
    padding-bottom: var(--bookinn-space-2, 0.5rem);
    border-bottom: 2px solid var(--bookinn-primary, #2563eb);
}

/* Responsive Design for Sub-tabs */
@media (max-width: 768px) {
    .bookinn-sub-tabs-nav {
        flex-direction: column;
    }

    .bookinn-sub-tab-link {
        justify-content: center;
        padding: var(--bookinn-space-3, 0.75rem) var(--bookinn-space-4, 1rem);
    }

    .bookinn-guests-table .bookinn-actions {
        flex-direction: column;
        gap: 2px;
    }

    .bookinn-modal#bookinn-guest-modal .bookinn-form-grid-2 {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .bookinn-sub-tab-link {
        font-size: var(--bookinn-text-sm, 0.875rem);
        padding: var(--bookinn-space-2, 0.5rem) var(--bookinn-space-3, 0.75rem);
    }

    .bookinn-guest-info {
        font-size: var(--bookinn-text-sm, 0.875rem);
    }
}

/* Booking Modal Enhancements */
.bookinn-pricing-calculator {
    background: var(--bookinn-bg-light, #f9fafb);
    border: 1px solid var(--bookinn-border, #e5e7eb);
    border-radius: var(--bookinn-radius-md, 6px);
    padding: var(--bookinn-space-4, 1rem);
    margin-top: var(--bookinn-space-4, 1rem);
}

.bookinn-price-display {
    font-size: var(--bookinn-text-lg, 1.125rem);
    font-weight: 600;
    color: var(--bookinn-primary, #2563eb);
    padding: var(--bookinn-space-2, 0.5rem) var(--bookinn-space-3, 0.75rem);
    background: white;
    border: 1px solid var(--bookinn-border, #e5e7eb);
    border-radius: var(--bookinn-radius-sm, 4px);
    text-align: center;
}

.bookinn-pricing-breakdown {
    margin-top: var(--bookinn-space-4, 1rem);
    padding: var(--bookinn-space-4, 1rem);
    background: white;
    border: 1px solid var(--bookinn-border, #e5e7eb);
    border-radius: var(--bookinn-radius-sm, 4px);
}

.bookinn-pricing-breakdown h6 {
    margin: 0 0 var(--bookinn-space-3, 0.75rem) 0;
    font-size: var(--bookinn-text-sm, 0.875rem);
    font-weight: 600;
    color: var(--bookinn-text-dark, #111827);
    border-bottom: 1px solid var(--bookinn-border, #e5e7eb);
    padding-bottom: var(--bookinn-space-2, 0.5rem);
}

.bookinn-breakdown-details {
    font-size: var(--bookinn-text-sm, 0.875rem);
    line-height: 1.5;
}

.bookinn-breakdown-details .breakdown-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--bookinn-space-1, 0.25rem) 0;
    border-bottom: 1px solid var(--bookinn-bg-light, #f9fafb);
}

.bookinn-breakdown-details .breakdown-item:last-child {
    border-bottom: none;
    font-weight: 600;
    color: var(--bookinn-text-dark, #111827);
}

.bookinn-breakdown-details .breakdown-label {
    color: var(--bookinn-text-muted, #6b7280);
}

.bookinn-breakdown-details .breakdown-value {
    color: var(--bookinn-primary, #2563eb);
    font-weight: 500;
}

/* Enhanced Form Sections */
.bookinn-form-section h5 i {
    margin-right: var(--bookinn-space-2, 0.5rem);
    color: var(--bookinn-primary, #2563eb);
    width: 16px;
    text-align: center;
}

.bookinn-help-text {
    display: block;
    font-size: var(--bookinn-text-xs, 0.75rem);
    color: var(--bookinn-text-muted, #6b7280);
    margin-top: var(--bookinn-space-1, 0.25rem);
    line-height: 1.4;
}

/* Enhanced Modal Sizing */
.bookinn-modal-lg .bookinn-modal-content {
    max-width: 900px;
    width: 95%;
}

/* Booking Options Styling */
.bookinn-form-section .bookinn-select {
    position: relative;
}

.bookinn-form-section .bookinn-select:focus + .bookinn-help-text {
    color: var(--bookinn-primary, #2563eb);
}

/* REMOVED: Calculate Button Styling - manual calculation button removed */

/* Responsive Enhancements */
@media (max-width: 768px) {
    .bookinn-modal-lg .bookinn-modal-content {
        width: 95%;
        max-width: 720px;
        margin: 10px;
    }

    .bookinn-pricing-calculator .bookinn-form-grid {
        grid-template-columns: 1fr;
        gap: var(--bookinn-space-3, 0.75rem);
    }

    .bookinn-price-display {
        font-size: var(--bookinn-text-base, 1rem);
    }
}

@media (max-width: 480px) {
    .bookinn-pricing-calculator {
        padding: var(--bookinn-space-3, 0.75rem);
    }

    .bookinn-form-section h5 {
        font-size: var(--bookinn-text-sm, 0.875rem);
    }

    .bookinn-form-section h5 i {
        font-size: var(--bookinn-text-xs, 0.75rem);
    }
}

/* Print Styles */
@media print {
    .bookinn-rate-plans-actions,
    .bookinn-rate-plans-filters,
    .bookinn-rate-plans-bulk-actions,
    .bookinn-rate-plan-actions,
    .bookinn-modal,
    .bookinn-sub-tabs-nav,
    #bookinn-guest-filters,
    .bookinn-pricing-calculator {
        display: none !important;
    }

    .bookinn-rate-plans-table,
    .bookinn-guests-table {
        border: 1px solid #000;
    }

    .bookinn-rate-plans-table th,
    .bookinn-rate-plans-table td,
    .bookinn-guests-table th,
    .bookinn-guests-table td {
        border: 1px solid #000;
        padding: 4px 8px;
    }

    .bookinn-row-inactive {
        opacity: 1;
        background: #f5f5f5;
    }

    .bookinn-sub-tab-panel {
        display: block !important;
    }
}
