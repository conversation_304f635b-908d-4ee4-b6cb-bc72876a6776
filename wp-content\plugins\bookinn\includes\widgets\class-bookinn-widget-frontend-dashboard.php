<?php
/**
 * BookInn Frontend Dashboard Widget
 * 
 * Main frontend interface with tabbed dashboard for hotel management
 */

if (!defined('ABSPATH')) {
    exit;
}

class BookInn_Widget_Frontend_Dashboard extends WP_Widget {
    
    /**
     * Constructor
     */
    public function __construct() {
        $widget_options = array(
            'classname' => 'bookinn-widget bookinn-frontend-dashboard-widget',
            'description' => __('Complete hotel management dashboard with tabs for bookings, rooms, calendar, and reports.', 'bookinn'),
            'customize_selective_refresh' => true,
        );
        
        $control_options = array(
            'width' => 600,
            'height' => 400
        );
        
        parent::__construct(
            'bookinn_frontend_dashboard',
            __('BookInn - Frontend Dashboard', 'bookinn'),
            $widget_options,
            $control_options
        );
        
        // Load Rate Plans Manager and Migration
        $this->load_rate_plans_manager();
        $this->load_rate_plans_migration();

        // Load Guest Registry Manager
        $this->load_guest_registry_manager();

        // Load Booking Enhancements
        $this->load_booking_enhancements();

        // Optimized single enqueue strategy
        add_action('wp_enqueue_scripts', array($this, 'enqueue_assets'), 10);
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));

        // Global fallback: ensure occupancy calendar script is enqueued on all front-end pages
        add_action('wp_enqueue_scripts', function() {
            if (!wp_script_is('bookinn-occupancy-calendar', 'enqueued')) {
                wp_enqueue_script('bookinn-occupancy-calendar',
                    BOOKINN_PLUGIN_URL . 'assets/js/bookinn-occupancy-calendar.js',
                    array('jquery'), BOOKINN_VERSION, true);
                // Provide a minimal localized config immediately so the calendar JS can initialize
                wp_localize_script('bookinn-occupancy-calendar', 'bookinnOccupancyConfig', array(
                    'ajax_url' => admin_url('admin-ajax.php'),
                    'nonce' => wp_create_nonce('bookinn_occupancy_nonce'),
                    'action' => 'bookinn_get_occupancy_data',
                    'texts' => array(
                        'loading' => __('Caricamento...', 'bookinn')
                    ),
                    'led_colors' => array(
                        'low' => array('color' => '#48bb78', 'range' => array(0, 30)),
                        'medium' => array('color' => '#ed8936', 'range' => array(31, 70)),
                        'high' => array('color' => '#f56565', 'range' => array(71, 100)),
                        'no_data' => '#e2e8f0'
                    )
                ));

                wp_add_inline_script('bookinn-occupancy-calendar', 'console.log("bookinnOccupancyConfig (global debug):", window.bookinnOccupancyConfig);', 'after');
            }
        }, 9);

        // Single fallback hook instead of multiple
        add_action('wp_footer', array($this, 'final_css_check'), 15);

        // Initialize Gantt AJAX handlers
        $this->init_gantt_ajax_handlers();
    }

    /**
     * Final CSS check - optimized single fallback
     */
    public function final_css_check() {
        if (!wp_style_is('bookinn-management-unified', 'done') || !wp_style_is('bookinn-management-unified', 'enqueued')) {
            $this->force_enqueue_assets();
        }
    }

    /**
     * Late enqueue check in footer
     */
    public function late_enqueue_check() {
        if (!wp_style_is('bookinn-management-unified', 'done')) {
            $this->force_enqueue_assets();
        }
    }

    /**
     * Emergency CSS check - absolute last resort
     */
    public function emergency_css_check() {
        if (!wp_style_is('bookinn-frontend-dashboard', 'done')) {
            // Check if widget is actually being displayed
            global $wp_registered_widgets;
            $widget_displayed = false;
            
            foreach ($wp_registered_widgets as $widget_id => $widget) {
                if (strpos($widget_id, 'bookinn_frontend_dashboard') !== false) {
                    $widget_displayed = true;
                    break;
                }
            }
            
            if ($widget_displayed) {
                $this->emergency_css_fallback();
            }
        }
    }

    /**
     * Enqueue frontend assets - Always enqueue when widget class is loaded
     */
    public function enqueue_assets() {
        // UNIFIED SYSTEM: Load only the unified CSS (legacy files removed)
        wp_enqueue_style('bookinn-management-unified',
            BOOKINN_PLUGIN_URL . 'assets/css/bookinn-management-unified.css',
            array(), '1.0.1-' . date('YmdHis'));

        // Enqueue occupancy calendar CSS for the new calendar implementation
        wp_enqueue_style('bookinn-occupancy-calendar',
            BOOKINN_PLUGIN_URL . 'assets/css/bookinn-occupancy-calendar.css',
            array('bookinn-management-unified'), BOOKINN_VERSION . '-' . time());

        // Enqueue vendor libraries (minified versions)
        wp_enqueue_script('bookinn-chartjs', 
            BOOKINN_PLUGIN_URL . 'assets/vendor/chart.min.js', 
            array(), '4.4.0', true);
    // FullCalendar non utilizzato: rimosso
        wp_enqueue_script('bookinn-flatpickr', 
            BOOKINN_PLUGIN_URL . 'assets/vendor/flatpickr.min.js', 
            array('jquery'), '4.6.13', true);
        wp_enqueue_style('bookinn-flatpickr', 
            BOOKINN_PLUGIN_URL . 'assets/vendor/flatpickr.min.css', 
            array(), '4.6.13');

        // CRITICAL FIX: Enqueue unified management system to resolve JavaScript errors
        // Load order is critical to prevent conflicts and ensure proper initialization
        if (wp_script_is('jquery', 'registered')) {

            // 0. Legacy disabler - Must load first to prevent conflicts
            wp_enqueue_script('bookinn-legacy-disable',
                BOOKINN_PLUGIN_URL . 'assets/js/bookinn-legacy-disable.js',
                array(), BOOKINN_VERSION, false); // Load in head, before other scripts

            // 1. Core BookInn main file - Contains unified modal system and base functionality
            wp_enqueue_script('bookinn-main',
                BOOKINN_PLUGIN_URL . 'assets/js/bookinn-main.js',
                array('jquery', 'bookinn-legacy-disable'), BOOKINN_VERSION, true);

            // 2. Unified management system - Main consolidated functionality
            // FIXES: Syntax error, AJAX authentication, room type editing, event handlers
            wp_enqueue_script('bookinn-management-unified',
                BOOKINN_PLUGIN_URL . 'assets/js/bookinn-management-unified.js',
                array('jquery', 'bookinn-main', 'bookinn-legacy-disable'), time(), true);

            // 3. Migration helper - Provides backward compatibility and system health monitoring
            wp_enqueue_script('bookinn-migration-helper',
                BOOKINN_PLUGIN_URL . 'assets/js/bookinn-migration-helper.js',
                array('jquery', 'bookinn-main', 'bookinn-management-unified'), BOOKINN_VERSION, true);

            // Legacy dashboard file removed - functionality moved to unified system

            // 4. Rate Plans Management - Independent module for rate plans functionality
            wp_enqueue_script('bookinn-rate-plans',
                BOOKINN_PLUGIN_URL . 'assets/js/bookinn-rate-plans.js',
                array('jquery', 'bookinn-main'), BOOKINN_VERSION, true);

            // 5. Guest Registry Management - Independent module for guest registry functionality
            wp_enqueue_script('bookinn-guest-registry',
                BOOKINN_PLUGIN_URL . 'assets/js/bookinn-guest-registry.js',
                array('jquery', 'bookinn-main'), BOOKINN_VERSION, true);

            // 6. Enhanced Booking Management - Enhanced booking functionality with pricing calculator
            wp_enqueue_script('bookinn-booking-enhanced',
                BOOKINN_PLUGIN_URL . 'assets/js/bookinn-booking-enhanced.js',
                array('jquery', 'bookinn-main'), BOOKINN_VERSION, true);

            // Localize guest registry script so JS has ajax endpoints and nonce
            wp_localize_script('bookinn-guest-registry', 'bookinn_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                // use unified nonce name expected by AJAX handlers
                'nonce'    => wp_create_nonce('bookinn_nonce'),
                'strings'  => array(
                    'confirmDelete' => __('Are you sure you want to delete this guest?', 'bookinn'),
                    'loading' => __('Loading...', 'bookinn'),
                    'error' => __('An error occurred', 'bookinn')
                )
            ));

            // Enqueue Rate Plans CSS (includes guest registry styles)
            wp_enqueue_style('bookinn-rate-plans',
                BOOKINN_PLUGIN_URL . 'assets/css/bookinn-rate-plans.css',
                array('bookinn-management-unified'), BOOKINN_VERSION);

            // Enqueue Rate Plans Debug Script (only in development/debug mode)
            if (defined('WP_DEBUG') && WP_DEBUG) {
                wp_enqueue_script('bookinn-rate-plans-debug',
                    BOOKINN_PLUGIN_URL . 'assets/js/bookinn-rate-plans-debug.js',
                    array('jquery', 'bookinn-rate-plans'), BOOKINN_VERSION, true);
            }

            // CRITICAL FIX: Localize script with proper nonce configuration
            // This fixes 403 Forbidden AJAX errors by providing both nonce types
            wp_localize_script('bookinn-management-unified', 'bookinn_dashboard', array(
                'ajax_url' => admin_url('admin-ajax.php'),                    // WordPress AJAX endpoint
                'rest_url' => rest_url('bookinn/v1/'),                       // REST API endpoint (future use)
                'plugin_url' => BOOKINN_PLUGIN_URL,                          // Plugin URL for assets
                'nonce' => wp_create_nonce('bookinn_dashboard_nonce'),       // Primary nonce - matches AJAX handler
                'ajax_nonce' => wp_create_nonce('bookinn_ajax'),             // Secondary nonce for management AJAX calls
                'management_nonce' => wp_create_nonce('bookinn_management_nonce'), // Specific nonce for management system
                'frontend_nonce' => wp_create_nonce('bookinn_frontend_nonce'), // Frontend specific nonce
                'rest_nonce' => wp_create_nonce('wp_rest'),                  // REST API nonce
                'debug_mode' => (defined('WP_DEBUG') && WP_DEBUG),          // Debug flag for reduced console noise
                'strings' => array(
                    'loading' => __('Loading...', 'bookinn'),
                    'error' => __('Error loading data', 'bookinn'),
                    'save_success' => __('Saved successfully', 'bookinn'),
                    'delete_confirm' => __('Are you sure you want to delete this item?', 'bookinn'),
                    'booking_saved' => __('Booking saved successfully', 'bookinn'),
                    'room_updated' => __('Room updated successfully', 'bookinn'),
                )
            ));

            // Localize Rate Plans script with dedicated configuration
            wp_localize_script('bookinn-rate-plans', 'bookinnRatePlans', array(
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('bookinn_rate_plans_nonce'),
                'strings' => array(
                    'confirmDelete' => __('Are you sure you want to delete this rate plan?', 'bookinn'),
                    'confirmBulkDelete' => __('Are you sure you want to delete the selected rate plans?', 'bookinn'),
                    'selectItems' => __('Please select items to perform bulk action', 'bookinn'),
                    'loading' => __('Loading...', 'bookinn'),
                    'error' => __('An error occurred', 'bookinn'),
                    'success' => __('Operation completed successfully', 'bookinn'),
                    'validationError' => __('Please fill in all required fields', 'bookinn'),
                    'dateError' => __('Please select a valid date', 'bookinn'),
                    'priceError' => __('Please enter a valid price', 'bookinn'),
                    'noRatePlans' => __('No rate plans found. Add your first rate plan to get started.', 'bookinn')
                )
            ));
        }
    }

    /**
     * Force assets enqueue on widget display
     */
    public function force_enqueue_assets() {
        // Force enqueue unified CSS immediately
        wp_enqueue_style('bookinn-management-unified',
            BOOKINN_PLUGIN_URL . 'assets/css/bookinn-management-unified.css',
            array(), BOOKINN_VERSION);
            
        // Force enqueue occupancy calendar CSS
        wp_enqueue_style('bookinn-occupancy-calendar',
            BOOKINN_PLUGIN_URL . 'assets/css/bookinn-occupancy-calendar.css',
            array('bookinn-management-unified'), BOOKINN_VERSION . '-' . time());
            
        // Add inline critical CSS to prevent FOUC - Enhanced Professional Style
        $inline_css = "
        .bookinn-dashboard-container {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            border-radius: 8px;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.06);
            margin: 12px 0;
            overflow: hidden;
            border: 1px solid #e2e8f0;
            line-height: 1.5;
        }
        .bookinn-tab-navigation {
            background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
            color: #fff;
            position: sticky;
            top: 0;
            z-index: 90;
        }
        .bookinn-tab-link {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            color: rgba(255, 255, 255, 0.85);
            text-decoration: none;
            font-weight: 500;
            font-size: 11px;
            min-height: 34px;
            border-bottom: 2px solid transparent;
            transition: all 0.15s ease-in-out;
        }
        .bookinn-tab-link:hover {
            color: #fff;
            background: rgba(255, 255, 255, 0.08);
        }
        .bookinn-tab-link.active {
            color: #fff;
            background: rgba(255, 255, 255, 0.12);
            border-bottom-color: rgba(255, 255, 255, 0.9);
            font-weight: 600;
        }
        .bookinn-tab-panel {
            display: none;
            padding: 20px;
        }
        .bookinn-tab-panel.active {
            display: block;
        }
        .bookinn-dashboard-content {
            display: grid;
            grid-template-columns: 1fr 280px;
            gap: 16px;
            padding: 16px;
            min-height: calc(85vh - 88px);
        }
        .bookinn-main-area {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.06);
            border: 1px solid #e2e8f0;
        }
        .bookinn-metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        .bookinn-metric-card {
            background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.06);
            border: 1px solid #e2e8f0;
            position: relative;
            overflow: hidden;
        }
        .bookinn-metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(to bottom, #1e40af, #0f766e);
        }
        .bookinn-metric-value {
            font-size: 18px;
            font-weight: 700;
            color: #0f172a;
            margin: 0 0 4px 0;
            line-height: 1;
            font-family: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
        }
        .bookinn-metric-label {
            font-size: 11px;
            color: #475569;
            margin: 0 0 6px 0;
            font-weight: 500;
        }
        .bookinn-sidebar {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        .bookinn-sidebar-widget {
            background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
            border-radius: 8px;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.06);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }
        @media (max-width: 1024px) {
            .bookinn-dashboard-content {
                grid-template-columns: 1fr;
            }
            .bookinn-metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        @media (max-width: 768px) {
            .bookinn-metrics-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            .bookinn-tab-panel {
                padding: 16px;
            }
            .bookinn-dashboard-content {
                padding: 12px;
                gap: 12px;
            }
        }
        /* Sub-tabs for room management */
        .bookinn-sub-tabs {
            background: white;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }
        .bookinn-sub-tab-nav {
            display: flex;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            padding: 0;
        }
        .bookinn-sub-tab-link {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            text-decoration: none;
            color: #64748b;
            font-weight: 500;
            font-size: 11px;
            border-bottom: 2px solid transparent;
            transition: all 0.15s ease-in-out;
            min-height: 32px;
        }
        .bookinn-sub-tab-link:hover {
            color: #1e40af;
            background: rgba(30, 64, 175, 0.05);
        }
        .bookinn-sub-tab-link.active {
            color: #1e40af;
            background: white;
            border-bottom-color: #1e40af;
            font-weight: 600;
        }
        .bookinn-sub-tab-panel {
            display: none;
            padding: 16px;
        }
        .bookinn-sub-tab-panel.active {
            display: block;
        }
        ";
        wp_add_inline_style('bookinn-frontend-dashboard', $inline_css);

        // Ensure occupancy calendar script is present when widget is rendered (debug + fallback)
        wp_enqueue_script('bookinn-occupancy-calendar',
            BOOKINN_PLUGIN_URL . 'assets/js/bookinn-occupancy-calendar.js',
            array('jquery'), BOOKINN_VERSION, true);

        // Add a small inline debug log to help verify localized config availability
        wp_add_inline_script('bookinn-occupancy-calendar', 'console.log("bookinnOccupancyConfig (debug):", window.bookinnOccupancyConfig);', 'after');
        
        // Enqueue the Gantt CSS for widget integration styles
        wp_enqueue_style(
            'bookinn-gantt-widget',
            BOOKINN_PLUGIN_URL . 'assets/css/bookinn-gantt.css',
            array('bookinn-frontend-dashboard'),
            BOOKINN_VERSION . '-gantt-fix-' . time() // Cache bust for layout fixes
        );
    }

    /**
     * Check if CSS is loaded and load if necessary
     */
    public function ensure_css_loaded() {
        // Check if our CSS is already enqueued
        if (!wp_style_is('bookinn-management-unified', 'enqueued')) {
            // CSS not loaded, force it
            $this->force_enqueue_assets();
        }
    }

    /**
     * Emergency CSS fallback - inline all critical styles
     */
    private function emergency_css_fallback() {
        $css_file = BOOKINN_PLUGIN_URL . 'assets/css/bookinn-management-unified.css';
        $css_path = str_replace(BOOKINN_PLUGIN_URL, BOOKINN_PLUGIN_PATH, $css_file);
        if (file_exists($css_path)) {
            $css_content = file_get_contents($css_path);
            echo '<style type="text/css" id="bookinn-emergency-css">' . $css_content . '</style>';
        }
    }

    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        if ('widgets.php' === $hook) {
            wp_enqueue_style('bookinn-widget-admin',
                BOOKINN_PLUGIN_URL . 'assets/css/bookinn-widget-admin.css',
                array(), BOOKINN_VERSION);
        }
    }

    /**
     * Display the widget content
     */
    public function widget($args, $instance) {
        // Check user permissions
        if (!$this->check_permissions()) {
            echo '<div class="bookinn-error">' . __('You do not have permission to access this dashboard.', 'bookinn') . '</div>';
            return;
        }

        // Ensure CSS is loaded with multiple fallback strategies
        $this->ensure_css_loaded();
        
        // Emergency fallback if CSS still not loaded
        if (!wp_style_is('bookinn-frontend-dashboard', 'done')) {
            $this->emergency_css_fallback();
        }

        echo $args['before_widget'];

        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }

        $this->render_dashboard($instance);

        echo $args['after_widget'];
    }

    /**
     * Widget form in admin
     */
    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : __('Hotel Dashboard', 'bookinn');
        $show_sidebar = isset($instance['show_sidebar']) ? (bool) $instance['show_sidebar'] : true;
        $default_tab = !empty($instance['default_tab']) ? $instance['default_tab'] : 'dashboard';
        $user_roles = !empty($instance['user_roles']) ? $instance['user_roles'] : array('administrator');
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php _e('Title:', 'bookinn'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>" 
                   name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text" 
                   value="<?php echo esc_attr($title); ?>">
        </p>
        
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('default_tab')); ?>"><?php _e('Default Tab:', 'bookinn'); ?></label>
            <select class="widefat" id="<?php echo esc_attr($this->get_field_id('default_tab')); ?>" 
                    name="<?php echo esc_attr($this->get_field_name('default_tab')); ?>">
                <option value="dashboard" <?php selected($default_tab, 'dashboard'); ?>><?php _e('Dashboard', 'bookinn'); ?></option>
                <option value="bookings" <?php selected($default_tab, 'bookings'); ?>><?php _e('Bookings', 'bookinn'); ?></option>
                <option value="rooms" <?php selected($default_tab, 'rooms'); ?>><?php _e('Rooms', 'bookinn'); ?></option>
                <option value="calendar" <?php selected($default_tab, 'calendar'); ?>><?php _e('Calendar', 'bookinn'); ?></option>
                <option value="reports" <?php selected($default_tab, 'reports'); ?>><?php _e('Reports', 'bookinn'); ?></option>
            </select>
        </p>
        
        <p>
            <input class="checkbox" type="checkbox" <?php checked($show_sidebar); ?> 
                   id="<?php echo esc_attr($this->get_field_id('show_sidebar')); ?>" 
                   name="<?php echo esc_attr($this->get_field_name('show_sidebar')); ?>" />
            <label for="<?php echo esc_attr($this->get_field_id('show_sidebar')); ?>"><?php _e('Show Sidebar', 'bookinn'); ?></label>
        </p>
        
        <p>
            <label><?php _e('User Roles (who can access):', 'bookinn'); ?></label><br>
            <?php
            $available_roles = wp_roles()->get_names();
            foreach ($available_roles as $role_key => $role_name) {
                $checked = in_array($role_key, $user_roles) ? 'checked' : '';
                ?>
                <label style="display: block; margin: 2px 0;">
                    <input type="checkbox" name="<?php echo esc_attr($this->get_field_name('user_roles')); ?>[]" 
                           value="<?php echo esc_attr($role_key); ?>" <?php echo $checked; ?>>
                    <?php echo esc_html($role_name); ?>
                </label>
                <?php
            }
            ?>
        </p>
        <?php
    }

    /**
     * Update widget settings
     */
    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        $instance['show_sidebar'] = isset($new_instance['show_sidebar']) ? (bool) $new_instance['show_sidebar'] : false;
        $instance['default_tab'] = (!empty($new_instance['default_tab'])) ? sanitize_text_field($new_instance['default_tab']) : 'dashboard';
        $instance['user_roles'] = (!empty($new_instance['user_roles']) && is_array($new_instance['user_roles'])) 
            ? array_map('sanitize_text_field', $new_instance['user_roles']) : array('administrator');
        
        return $instance;
    }

    /**
     * Check user permissions
     */
    private function check_permissions() {
        if (!is_user_logged_in()) {
            return false;
        }
        
        $user = wp_get_current_user();
        $allowed_roles = array('administrator', 'editor'); // Default roles
        
        // Get widget instance to check configured roles
        $widget_instances = $this->get_settings();
        if (!empty($widget_instances)) {
            foreach ($widget_instances as $instance) {
                if (!empty($instance['user_roles'])) {
                    $allowed_roles = $instance['user_roles'];
                    break;
                }
            }
        }
        
        return array_intersect($allowed_roles, $user->roles) ? true : false;
    }

    /**
     * Render main dashboard
     */
    private function render_dashboard($instance) {
        $default_tab = $instance['default_tab'] ?? 'dashboard';
        $show_sidebar = $instance['show_sidebar'] ?? true;
        ?>
        <div class="bookinn-dashboard-container" data-default-tab="<?php echo esc_attr($default_tab); ?>">
            <!-- Dashboard Header -->
            <div class="bookinn-dashboard-header">
                <div class="bookinn-header-content">
                    <img src="<?php echo BOOKINN_PLUGIN_URL . 'assets/images/logo_header_widget.png'; ?>" alt="BookInn Logo" class="bookinn-logo">
                    <h2 class="bookinn-dashboard-title"><?php _e('Hotel Management Dashboard', 'bookinn'); ?></h2>
                    <div class="bookinn-header-actions">
                        <div class="bookinn-user-menu">
                            <span class="bookinn-user-name"><?php echo esc_html(wp_get_current_user()->display_name); ?></span>
                            <div class="bookinn-notifications">
                                <span class="bookinn-notification-badge" id="bookinn-notification-count">0</span>
                                <i class="fa-solid fa-bell bookinn-fa"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <nav class="bookinn-tab-navigation" role="tablist" aria-label="<?php esc_attr_e('Dashboard Navigation', 'bookinn'); ?>">
                <div class="bookinn-nav-container">
                    <a href="#tab-dashboard" class="bookinn-tab-link active" data-tab="dashboard"
                       role="tab" aria-selected="true" aria-controls="tab-dashboard" tabindex="0">
                        <i class="fa-solid fa-gauge bookinn-fa" aria-hidden="true"></i>
                        <span><?php _e('Dashboard', 'bookinn'); ?></span>
                    </a>
                    <a href="#tab-bookings" class="bookinn-tab-link" data-tab="bookings"
                       role="tab" aria-selected="false" aria-controls="tab-bookings" tabindex="-1">
                        <i class="fa-solid fa-calendar-check bookinn-fa" aria-hidden="true"></i>
                        <span><?php _e('Bookings', 'bookinn'); ?></span>
                    </a>
                    <a href="#tab-rooms" class="bookinn-tab-link" data-tab="rooms"
                       role="tab" aria-selected="false" aria-controls="tab-rooms" tabindex="-1">
                        <i class="fa-solid fa-bed bookinn-fa" aria-hidden="true"></i>
                        <span><?php _e('Rooms', 'bookinn'); ?></span>
                    </a>
                    <a href="#tab-calendar" class="bookinn-tab-link" data-tab="calendar"
                       role="tab" aria-selected="false" aria-controls="tab-calendar" tabindex="-1">
                        <i class="fa-solid fa-calendar-days bookinn-fa" aria-hidden="true"></i>
                        <span><?php _e('Calendar', 'bookinn'); ?></span>
                    </a>
                    <a href="#tab-reports" class="bookinn-tab-link" data-tab="reports"
                       role="tab" aria-selected="false" aria-controls="tab-reports" tabindex="-1">
                        <i class="fa-solid fa-chart-line bookinn-fa" aria-hidden="true"></i>
                        <span><?php _e('Reports', 'bookinn'); ?></span>
                    </a>
                    <button class="bookinn-nav-toggle" id="bookinn-nav-toggle"
                            aria-label="<?php esc_attr_e('Toggle navigation menu', 'bookinn'); ?>"
                            aria-expanded="false">
                        <i class="fa-solid fa-bars bookinn-fa" aria-hidden="true"></i>
                    </button>
                </div>
            </nav>

            <!-- Main Content Area -->
            <div class="bookinn-dashboard-content">
                <div class="bookinn-main-area">
                    <!-- Tab Content Panels -->
                    <div class="bookinn-tab-panels">
                        <!-- Dashboard Tab -->
                        <div class="bookinn-tab-panel active" id="tab-dashboard"
                             role="tabpanel" aria-labelledby="tab-dashboard-link">
                            <?php $this->render_dashboard_tab(); ?>
                        </div>
                        
                        <!-- Bookings Tab -->
                        <div class="bookinn-tab-panel" id="tab-bookings"
                             role="tabpanel" aria-labelledby="tab-bookings-link" aria-hidden="true">
                            <?php $this->render_bookings_tab(); ?>
                        </div>
                        
                        <!-- Rooms Tab -->
                        <div class="bookinn-tab-panel" id="tab-rooms"
                             role="tabpanel" aria-labelledby="tab-rooms-link" aria-hidden="true">
                            <?php $this->render_rooms_tab(); ?>
                        </div>
                        
                        <!-- Calendar Tab with Sub-tabs -->
                        <div class="bookinn-tab-panel" id="tab-calendar"
                             role="tabpanel" aria-labelledby="tab-calendar-link" aria-hidden="true">
                            <div class="bookinn-calendar-tabs">
                                <div class="bookinn-sub-tab-nav">
                                     <a href="#calendar-gantt" class="bookinn-sub-tab-link active" data-tab="calendar-gantt">
                                        <?php _e('Booking Gantt Reservations', 'bookinn'); ?>
                                    </a>
                                    <a href="#calendar-main" class="bookinn-sub-tab-link" data-tab="calendar-main">
                                        <?php _e('Occupancy Calendar', 'bookinn'); ?>
                                    </a>
                                </div>
                                <!-- Sub-tab Panels -->
                                <div class="bookinn-sub-tab-panel" id="calendar-main">
                                    <?php $this->render_calendar_tab(); ?>
                                </div>
                                <div class="bookinn-sub-tab-panel active" id="calendar-gantt">
                                    <div class="bookinn-calendar-gantt-container">
                                        <!-- HEADER: Section Title & Description -->
                                        <div class="bookinn-section-header" style="display: flex; align-items: flex-start; justify-content: space-between; gap: 12px;">
                                            <div style="flex:1;">
                                                <h3 style="margin-bottom: 0;"><?php _e('Booking Gantt Reservations', 'bookinn'); ?></h3>
                                                <p class="bookinn-section-description" style="margin-top: 4px; margin-bottom: 0; color: #6b7280; font-size: 13px; font-weight: 400;">
                                                    <?php _e('Interactive timeline view of room reservations with drag & drop functionality', 'bookinn'); ?>
                                                </p>
                                            </div>
                                            <button class="bookinn-gantt-fullscreen-btn" title="Fullscreen" onclick="bookinnToggleGanttFullscreen(event)" style="background: none; border: none; cursor: pointer; padding: 6px; margin-left: 8px;">
                                                <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <rect x="3" y="3" width="6" height="2" rx="1" fill="#374151"/>
                                                    <rect x="3" y="3" width="2" height="6" rx="1" fill="#374151"/>
                                                    <rect x="13" y="3" width="6" height="2" rx="1" fill="#374151"/>
                                                    <rect x="17" y="3" width="2" height="6" rx="1" fill="#374151"/>
                                                    <rect x="3" y="17" width="6" height="2" rx="1" fill="#374151"/>
                                                    <rect x="3" y="13" width="2" height="6" rx="1" fill="#374151"/>
                                                    <rect x="13" y="17" width="6" height="2" rx="1" fill="#374151"/>
                                                    <rect x="17" y="13" width="2" height="6" rx="1" fill="#374151"/>
                                                </svg>
                                            </button>
                                        </div>


                                        <!-- Level 1 - Controls (View Mode, Period) + Legend allineata a destra -->
                                        <div class="bookinn-gantt-controls-row">
                                            <div class="bookinn-gantt-controls-left">
                                                <div class="bookinn-filter-group">
                                                    <label><?php _e('View Mode:', 'bookinn'); ?></label>
                                                    <select id="gantt-view-mode" class="bookinn-select" onchange="handleViewChange()">
                                                        <option value="month"><?php _e('Monthly Grid', 'bookinn'); ?></option>
                                                        <option value="week"><?php _e('Weekly View', 'bookinn'); ?></option>
                                                        <option value="custom"><?php _e('Custom Range', 'bookinn'); ?></option>
                                                    </select>
                                                </div>
                                                <div class="bookinn-filter-group">
                                                    <label><?php _e('Period:', 'bookinn'); ?></label>
                                                    <div class="bookinn-month-navigation">
                                                        <button type="button" onclick="previousMonth()" class="bookinn-nav-btn" title="<?php _e('Previous Month', 'bookinn'); ?>">
                                                            <i class="bookinn-icon">‹</i>
                                                        </button>
                                                        <div class="bookinn-current-period" id="currentPeriod"><?php _e('Loading...', 'bookinn'); ?></div>
                                                        <button type="button" onclick="nextMonth()" class="bookinn-nav-btn" title="<?php _e('Next Month', 'bookinn'); ?>">
                                                            <i class="bookinn-icon">›</i>
                                                        </button>
                                                        <button type="button" onclick="goToToday()" class="bookinn-btn bookinn-btn-sm bookinn-btn-outline"><?php _e('Today', 'bookinn'); ?></button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="bookinn-gantt-controls-right">
                                                <div class="bookinn-legend-title"><?php _e('Booking Status Legend:', 'bookinn'); ?></div>
                                                <div class="bookinn-calendar-legend">
                                                    <div class="bookinn-legend-item">
                                                        <div class="bookinn-legend-color bookinn-status-confirmed"></div>
                                                        <span><?php _e('Confirmed', 'bookinn'); ?></span>
                                                    </div>
                                                    <div class="bookinn-legend-item">
                                                        <div class="bookinn-legend-color bookinn-status-pending"></div>
                                                        <span><?php _e('Pending', 'bookinn'); ?></span>
                                                    </div>
                                                    <div class="bookinn-legend-item">
                                                        <div class="bookinn-legend-color bookinn-status-checked_in"></div>
                                                        <span><?php _e('Checked In', 'bookinn'); ?></span>
                                                    </div>
                                                    <div class="bookinn-legend-item">
                                                        <div class="bookinn-legend-color bookinn-status-checked_out"></div>
                                                        <span><?php _e('Checked Out', 'bookinn'); ?></span>
                                                    </div>
                                                    <div class="bookinn-legend-item">
                                                        <div class="bookinn-legend-color bookinn-status-cancelled"></div>
                                                        <span><?php _e('Cancelled', 'bookinn'); ?></span>
                                                    </div>
                                                    <div class="bookinn-legend-item">
                                                        <div class="bookinn-legend-color bookinn-status-no_show"></div>
                                                        <span><?php _e('No Show', 'bookinn'); ?></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Level 2 - Filter Controls, Date Picker & Actions (all inline) -->
                                        <div class="bookinn-gantt-level-2">
                                            <div class="bookinn-card-group bookinn-card-period">
                                                <div class="bookinn-filter-group">
                                                    <label><?php _e('Custom Period:', 'bookinn'); ?></label>
                                                    <div class="bookinn-date-range">
                                                        <input type="date" id="customStart" class="bookinn-date-input" onchange="handleDateChange()">
                                                        <span class="bookinn-date-separator"><?php _e('to', 'bookinn'); ?></span>
                                                        <input type="date" id="customEnd" class="bookinn-date-input" onchange="handleDateChange()">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="bookinn-card-group bookinn-card-filters">
                                                <div class="bookinn-filter-group">
                                                    <label><?php _e('Room Type:', 'bookinn'); ?></label>
                                                    <select id="gantt-room-type-filter" class="bookinn-select" onchange="handleFilterChange()">
                                                        <option value=""><?php _e('All Types', 'bookinn'); ?></option>
                                                        <?php $this->render_room_type_options(); ?>
                                                    </select>
                                                </div>
                                                <div class="bookinn-filter-group">
                                                    <label><?php _e('Booking Status:', 'bookinn'); ?></label>
                                                    <select id="gantt-booking-status-filter" class="bookinn-select" onchange="handleFilterChange()">
                                                        <option value=""><?php _e('All', 'bookinn'); ?></option>
                                                        <option value="confirmed"><?php _e('Confirmed', 'bookinn'); ?></option>
                                                        <option value="pending"><?php _e('Pending', 'bookinn'); ?></option>
                                                        <option value="checked_in"><?php _e('Checked In', 'bookinn'); ?></option>
                                                        <option value="cancelled"><?php _e('Cancelled', 'bookinn'); ?></option>
                                                    </select>
                                                </div>
                                                <div class="bookinn-filter-group">
                                                    <label><?php _e('Room Status:', 'bookinn'); ?></label>
                                                    <select id="gantt-room-status-filter" class="bookinn-select" onchange="handleFilterChange()">
                                                        <option value=""><?php _e('All', 'bookinn'); ?></option>
                                                        <option value="available"><?php _e('Available', 'bookinn'); ?></option>
                                                        <option value="occupied"><?php _e('Occupied', 'bookinn'); ?></option>
                                                        <option value="maintenance"><?php _e('Maintenance', 'bookinn'); ?></option>
                                                        <option value="out_of_order"><?php _e('Out of Order', 'bookinn'); ?></option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="bookinn-filter-actions">
                                                <button type="button" onclick="applyGanttFilters()" class="bookinn-btn bookinn-btn-primary bookinn-btn-sm">
                                                    <?php _e('Apply Filters', 'bookinn'); ?>
                                                </button>
                                                <button type="button" onclick="resetGanttFilters()" class="bookinn-btn bookinn-btn-secondary bookinn-btn-sm">
                                                    <?php _e('Reset Filters', 'bookinn'); ?>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Gantt Content -->
                                        <div class="bookinn-calendar-gantt-content">
                                            <div class="bookinn-gantt-wrapper">
                                                <?php echo $this->render_gantt_chart(); ?>
                                            </div>
                                        </div>
                                        
                                        <script>
                                        // Solo handler per i filtri: delega tutto al JS globale
                                        function handleFilterChange() {
                                            if (typeof applyGanttFilters === 'function') applyGanttFilters();
                                        }
                                        function handleDateChange() {
                                            const customStart = document.getElementById('customStart')?.value;
                                            const customEnd = document.getElementById('customEnd')?.value;
                                            if (customStart && customEnd && typeof setCustomPeriod === 'function') setCustomPeriod(customStart, customEnd);
                                        }
                                        // Inizializzazione Gantt widget: solo bootstrap, nessuna logica duplicata
                                        function initializeWidgetGantt() {
                                            var widgetElement = document.getElementById('widgetGanttChart');
                                            if (widgetElement && typeof fetchRoomsAndBookings === 'function' && typeof renderGanttChart === 'function') {
                                                window.ganttConfig = {
                                                    headerRowId: 'widgetHeaderRow',
                                                    bodyId: 'widgetGanttBody',
                                                    tableId: 'widgetGanttTable'
                                                };
                                                fetchRoomsAndBookings();
                                            } else if (widgetElement) {
                                                setTimeout(initializeWidgetGantt, 500);
                                            }
                                        }
                                        document.addEventListener('DOMContentLoaded', function() {
                                            setTimeout(initializeWidgetGantt, 100);
                                        });
                                        </script>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Reports Tab -->
                        <div class="bookinn-tab-panel" id="tab-reports"
                             role="tabpanel" aria-labelledby="tab-reports-link" aria-hidden="true">
                            <?php $this->render_reports_tab(); ?>
                        </div>
                    </div>
                </div>
                
                <?php if ($show_sidebar): ?>
                <!-- Sidebar -->
                <div class="bookinn-sidebar">
                    <?php $this->render_sidebar(); ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <?php
    }

    /**
     * Render Dashboard Tab
     */
    private function render_dashboard_tab() {
        global $wpdb;

        // Get dashboard metrics
        $metrics = $this->get_dashboard_metrics();
        ?>
        <div class="bookinn-dashboard-overview">
            <!-- Metrics Cards -->
            <div class="bookinn-metrics-grid">
                <div class="bookinn-metric-card">
                    <div class="bookinn-metric-icon">
                        <i class="fa-solid fa-calendar-check bookinn-fa"></i>
                    </div>
                    <div class="bookinn-metric-content">
                        <h3 class="bookinn-metric-value"><?php echo esc_html($metrics['total_bookings']); ?></h3>
                        <p class="bookinn-metric-label"><?php _e('Total Bookings', 'bookinn'); ?></p>
                        <span class="bookinn-metric-change positive">+<?php echo esc_html($metrics['bookings_change']); ?>%</span>
                    </div>
                </div>

                <div class="bookinn-metric-card">
                    <div class="bookinn-metric-icon">
                        <i class="fa-solid fa-euro-sign bookinn-fa"></i>
                    </div>
                    <div class="bookinn-metric-content">
                        <h3 class="bookinn-metric-value">€<?php echo esc_html(number_format($metrics['total_revenue'], 2)); ?></h3>
                        <p class="bookinn-metric-label"><?php _e('Total Revenue', 'bookinn'); ?></p>
                        <span class="bookinn-metric-change positive">+<?php echo esc_html($metrics['revenue_change']); ?>%</span>
                    </div>
                </div>

                <div class="bookinn-metric-card">
                    <div class="bookinn-metric-icon">
                        <i class="fa-solid fa-percent bookinn-fa"></i>
                    </div>
                    <div class="bookinn-metric-content">
                        <h3 class="bookinn-metric-value"><?php echo esc_html($metrics['occupancy_rate']); ?>%</h3>
                        <p class="bookinn-metric-label"><?php _e('Occupancy Rate', 'bookinn'); ?></p>
                        <span class="bookinn-metric-change <?php echo $metrics['occupancy_change'] >= 0 ? 'positive' : 'negative'; ?>">
                            <?php echo $metrics['occupancy_change'] >= 0 ? '+' : ''; ?><?php echo esc_html($metrics['occupancy_change']); ?>%
                        </span>
                    </div>
                </div>

                <div class="bookinn-metric-card">
                    <div class="bookinn-metric-icon">
                        <i class="fa-solid fa-bed bookinn-fa"></i>
                    </div>
                    <div class="bookinn-metric-content">
                        <h3 class="bookinn-metric-value"><?php echo esc_html($metrics['available_rooms']); ?></h3>
                        <p class="bookinn-metric-label"><?php _e('Available Rooms', 'bookinn'); ?></p>
                        <span class="bookinn-metric-subtitle"><?php _e('Today', 'bookinn'); ?></span>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="bookinn-charts-section">
                <div class="bookinn-chart-container">
                    <div class="bookinn-chart-header">
                        <h3><?php _e('Revenue Trend', 'bookinn'); ?></h3>
                        <div class="bookinn-chart-controls">
                            <select id="bookinn-revenue-period">
                                <option value="7"><?php _e('Last 7 days', 'bookinn'); ?></option>
                                <option value="30" selected><?php _e('Last 30 days', 'bookinn'); ?></option>
                                <option value="90"><?php _e('Last 3 months', 'bookinn'); ?></option>
                            </select>
                        </div>
                    </div>
                    <div class="bookinn-chart-wrapper">
                        <canvas id="bookinn-revenue-chart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="bookinn-chart-container">
                    <div class="bookinn-chart-header">
                        <h3><?php _e('Occupancy Forecast', 'bookinn'); ?></h3>
                        <div class="bookinn-chart-controls">
                            <select id="bookinn-forecast-period">
                                <option value="7"><?php _e('Next 7 days', 'bookinn'); ?></option>
                                <option value="30" selected><?php _e('Next 30 days', 'bookinn'); ?></option>
                                <option value="90"><?php _e('Next 3 months', 'bookinn'); ?></option>
                            </select>
                        </div>
                    </div>
                    <div class="bookinn-chart-wrapper">
                        <canvas id="bookinn-forecast-chart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Bottom Row: Recent Activity and Charts -->
            <div class="bookinn-dashboard-bottom-row">
                <!-- Recent Activity (Compact) -->
                <div class="bookinn-recent-activity bookinn-compact">
                    <div class="bookinn-activity-header">
                        <h3><?php _e('Recent Activity', 'bookinn'); ?></h3>
                        <a href="#tab-bookings" class="bookinn-view-all"><?php _e('View All', 'bookinn'); ?></a>
                    </div>
                    <div class="bookinn-activity-list" id="bookinn-recent-activity">
                        <?php $this->render_recent_activity(); ?>
                    </div>
                </div>

                <!-- Booking Analytics Charts -->
                <div class="bookinn-booking-analytics">
                    <!-- Booking Days Chart -->
                    <div class="bookinn-chart-card">
                        <div class="bookinn-chart-header">
                            <h3><?php _e('Booking Days', 'bookinn'); ?></h3>
                            <select id="booking-days-period" class="bookinn-select-sm">
                                <option value="7"><?php _e('Last 7 days', 'bookinn'); ?></option>
                                <option value="30" selected><?php _e('Last 30 days', 'bookinn'); ?></option>
                                <option value="90"><?php _e('Last 90 days', 'bookinn'); ?></option>
                            </select>
                        </div>
                        <div class="bookinn-chart-container">
                            <canvas id="bookinn-booking-days-chart" width="400" height="200"></canvas>
                        </div>
                    </div>

                    <!-- Booking Rates Chart -->
                    <div class="bookinn-chart-card">
                        <div class="bookinn-chart-header">
                            <h3><?php _e('Booking Rates', 'bookinn'); ?></h3>
                            <select id="booking-rates-period" class="bookinn-select-sm">
                                <option value="7"><?php _e('Last 7 days', 'bookinn'); ?></option>
                                <option value="30" selected><?php _e('Last 30 days', 'bookinn'); ?></option>
                                <option value="90"><?php _e('Last 90 days', 'bookinn'); ?></option>
                            </select>
                        </div>
                        <div class="bookinn-chart-container">
                            <canvas id="bookinn-booking-rates-chart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Get dashboard metrics - Enhanced with more analytics
     */
    private function get_dashboard_metrics() {
        global $wpdb;

        $today = date('Y-m-d');
        $last_month = date('Y-m-d', strtotime('-30 days'));
        $prev_month = date('Y-m-d', strtotime('-60 days'));
        $last_week = date('Y-m-d', strtotime('-7 days'));
        $last_year = date('Y-m-d', strtotime('-365 days'));

        // Enhanced booking metrics
        $total_bookings = BookInn_Database_Manager::get_var(
            "SELECT COUNT(*) FROM {bookings} WHERE created_at >= %s",
            $last_month
        ) ?: 0;

        $prev_bookings = BookInn_Database_Manager::get_var(
            "SELECT COUNT(*) FROM {bookings} WHERE created_at >= %s AND created_at < %s",
            $prev_month, $last_month
        ) ?: 0;

        $weekly_bookings = BookInn_Database_Manager::get_var(
            "SELECT COUNT(*) FROM {bookings} WHERE created_at >= %s",
            $last_week
        ) ?: 0;

        $bookings_change = $prev_bookings > 0 ? round((($total_bookings - $prev_bookings) / $prev_bookings) * 100, 1) : 0;

        // Enhanced revenue metrics
        $total_revenue = BookInn_Database_Manager::get_var(
            "SELECT SUM(total_amount) FROM {bookings} WHERE created_at >= %s AND status IN ('confirmed', 'checked_out')",
            $last_month
        ) ?: 0;

        $prev_revenue = BookInn_Database_Manager::get_var(
            "SELECT SUM(total_amount) FROM {bookings} WHERE created_at >= %s AND created_at < %s AND status IN ('confirmed', 'checked_out')",
            $prev_month, $last_month
        ) ?: 0;

        $weekly_revenue = BookInn_Database_Manager::get_var(
            "SELECT SUM(total_amount) FROM {bookings} WHERE created_at >= %s AND status IN ('confirmed', 'checked_out')",
            $last_week
        ) ?: 0;

        $revenue_change = $prev_revenue > 0 ? round((($total_revenue - $prev_revenue) / $prev_revenue) * 100, 1) : 0;

        // Advanced occupancy calculations
        $total_rooms = BookInn_Database_Manager::get_var("SELECT COUNT(*) FROM {rooms} WHERE is_active = 1") ?: 1;
        $occupied_rooms = BookInn_Database_Manager::get_var(
            "SELECT COUNT(DISTINCT room_id) FROM {bookings} WHERE check_in_date <= %s AND check_out_date > %s AND status IN ('confirmed', 'checked_in')",
            $today, $today
        ) ?: 0;

        $occupancy_rate = round(($occupied_rooms / $total_rooms) * 100, 1);
        
        // Weekly occupancy for comparison (fixed SQL)
        $weekly_avg_occupancy = BookInn_Database_Manager::get_var(
            "SELECT AVG(daily_occupancy) FROM ("
            . " SELECT COUNT(DISTINCT b.room_id) / %d * 100 as daily_occupancy"
            . " FROM (SELECT 0 as day_offset UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6) d"
            . " LEFT JOIN {bookings} b"
            . " ON b.check_in_date <= DATE_SUB(CURDATE(), INTERVAL d.day_offset DAY)"
            . " AND b.check_out_date > DATE_SUB(CURDATE(), INTERVAL d.day_offset DAY)"
            . " AND b.status IN ('confirmed', 'checked_in')"
            . " GROUP BY d.day_offset"
            . ") weekly_occupancy",
            $total_rooms
        ) ?: 0;

        $occupancy_change = round($occupancy_rate - $weekly_avg_occupancy, 1);

        // Available rooms today
        $available_rooms = $total_rooms - $occupied_rooms;

        // Additional KPIs
        $avg_daily_rate = $total_revenue > 0 && $total_bookings > 0 ? round($total_revenue / $total_bookings, 2) : 0;
        $revpar = round(($total_revenue / 30) / $total_rooms, 2); // Revenue Per Available Room
        
        // Cancellation rate
        $cancelled_bookings = BookInn_Database_Manager::get_var(
            "SELECT COUNT(*) FROM {bookings} WHERE created_at >= %s AND status = 'cancelled'",
            $last_month
        ) ?: 0;
        
        $cancellation_rate = $total_bookings > 0 ? round(($cancelled_bookings / $total_bookings) * 100, 1) : 0;

        // Average length of stay
        $avg_stay_length = BookInn_Database_Manager::get_var(
            "SELECT AVG(DATEDIFF(check_out_date, check_in_date)) FROM {bookings} 
             WHERE created_at >= %s AND status IN ('confirmed', 'checked_out')",
            $last_month
        ) ?: 0;

        // Guest satisfaction (mock data - replace with real survey data)
        $guest_satisfaction = 4.2; // Out of 5

        return array(
            'total_bookings' => $total_bookings,
            'bookings_change' => $bookings_change,
            'weekly_bookings' => $weekly_bookings,
            'total_revenue' => $total_revenue,
            'revenue_change' => $revenue_change,
            'weekly_revenue' => $weekly_revenue,
            'occupancy_rate' => $occupancy_rate,
            'occupancy_change' => $occupancy_change,
            'available_rooms' => $available_rooms,
            'avg_daily_rate' => $avg_daily_rate,
            'revpar' => $revpar,
            'cancellation_rate' => $cancellation_rate,
            'avg_stay_length' => round($avg_stay_length, 1),
            'guest_satisfaction' => $guest_satisfaction
        );
    }

    /**
     * Get reports metrics for the reports tab
     */
    private function get_reports_metrics() {
        global $wpdb;

        $today = date('Y-m-d');
        $last_month = date('Y-m-d', strtotime('-30 days'));
        $prev_month = date('Y-m-d', strtotime('-60 days'));
        $last_week = date('Y-m-d', strtotime('-7 days'));

        // Current period metrics (last 30 days)
        $total_bookings = BookInn_Database_Manager::get_var(
            "SELECT COUNT(*) FROM {bookings} WHERE created_at >= %s",
            $last_month
        ) ?: 0;

        $total_revenue = BookInn_Database_Manager::get_var(
            "SELECT SUM(total_amount) FROM {bookings} WHERE created_at >= %s AND status IN ('confirmed', 'checked_out')",
            $last_month
        ) ?: 0;

        $cancelled_bookings = BookInn_Database_Manager::get_var(
            "SELECT COUNT(*) FROM {bookings} WHERE created_at >= %s AND status = 'cancelled'",
            $last_month
        ) ?: 0;

        $avg_stay_length = BookInn_Database_Manager::get_var(
            "SELECT AVG(DATEDIFF(check_out_date, check_in_date)) FROM {bookings} 
             WHERE created_at >= %s AND status IN ('confirmed', 'checked_out')",
            $last_month
        ) ?: 0;

        // Previous period metrics (30-60 days ago)
        $prev_bookings = BookInn_Database_Manager::get_var(
            "SELECT COUNT(*) FROM {bookings} WHERE created_at >= %s AND created_at < %s",
            $prev_month, $last_month
        ) ?: 0;

        $prev_revenue = BookInn_Database_Manager::get_var(
            "SELECT SUM(total_amount) FROM {bookings} WHERE created_at >= %s AND created_at < %s AND status IN ('confirmed', 'checked_out')",
            $prev_month, $last_month
        ) ?: 0;

        $prev_cancelled_bookings = BookInn_Database_Manager::get_var(
            "SELECT COUNT(*) FROM {bookings} WHERE created_at >= %s AND created_at < %s AND status = 'cancelled'",
            $prev_month, $last_month
        ) ?: 0;

        $prev_avg_stay = BookInn_Database_Manager::get_var(
            "SELECT AVG(DATEDIFF(check_out_date, check_in_date)) FROM {bookings} 
             WHERE created_at >= %s AND created_at < %s AND status IN ('confirmed', 'checked_out')",
            $prev_month, $last_month
        ) ?: 0;

        // Calculate changes
        $bookings_change = $prev_bookings > 0 ? round((($total_bookings - $prev_bookings) / $prev_bookings) * 100, 1) : 0;
        $revenue_change = $prev_revenue > 0 ? round((($total_revenue - $prev_revenue) / $prev_revenue) * 100, 1) : 0;

        // Occupancy calculations
        $total_rooms = BookInn_Database_Manager::get_var("SELECT COUNT(*) FROM {rooms} WHERE is_active = 1") ?: 1;
        $occupied_rooms = BookInn_Database_Manager::get_var(
            "SELECT COUNT(DISTINCT room_id) FROM {bookings} WHERE check_in_date <= %s AND check_out_date > %s AND status IN ('confirmed', 'checked_in')",
            $today, $today
        ) ?: 0;

        $occupancy_rate = round(($occupied_rooms / $total_rooms) * 100, 1);

        // Previous period occupancy (simplified calculation)
        $prev_occupancy_rate = BookInn_Database_Manager::get_var(
            "SELECT AVG(daily_occupancy) FROM ("
            . " SELECT COUNT(DISTINCT b.room_id) / %d * 100 as daily_occupancy"
            . " FROM (SELECT 0 as day_offset UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6) d"
            . " LEFT JOIN {bookings} b"
            . " ON b.check_in_date <= DATE_SUB(%s, INTERVAL d.day_offset DAY)"
            . " AND b.check_out_date > DATE_SUB(%s, INTERVAL d.day_offset DAY)"
            . " AND b.status IN ('confirmed', 'checked_in')"
            . " GROUP BY d.day_offset"
            . ") prev_occupancy",
            $total_rooms, $prev_month, $prev_month
        ) ?: 0;

        $occupancy_change = round($occupancy_rate - $prev_occupancy_rate, 1);

        // ADR (Average Daily Rate) calculations
        $avg_daily_rate = $total_bookings > 0 ? round($total_revenue / $total_bookings, 2) : 0;
        $prev_avg_daily_rate = $prev_bookings > 0 ? round($prev_revenue / $prev_bookings, 2) : 0;
        $adr_change = $prev_avg_daily_rate > 0 ? round((($avg_daily_rate - $prev_avg_daily_rate) / $prev_avg_daily_rate) * 100, 1) : 0;

        // RevPAR (Revenue Per Available Room) calculations
        $revpar = round(($total_revenue / 30) / $total_rooms, 2);
        $prev_revpar = round(($prev_revenue / 30) / $total_rooms, 2);
        $revpar_change = $prev_revpar > 0 ? round((($revpar - $prev_revpar) / $prev_revpar) * 100, 1) : 0;

        // Cancellation rates
        $cancellation_rate = $total_bookings > 0 ? round(($cancelled_bookings / $total_bookings) * 100, 1) : 0;
        $prev_cancellation_rate = $prev_bookings > 0 ? round(($prev_cancelled_bookings / $prev_bookings) * 100, 1) : 0;
        $cancellation_change = round($cancellation_rate - $prev_cancellation_rate, 1);

        // Stay length changes
        $stay_change = $prev_avg_stay > 0 ? round((($avg_stay_length - $prev_avg_stay) / $prev_avg_stay) * 100, 1) : 0;

        return array(
            'total_bookings' => $total_bookings,
            'prev_bookings' => $prev_bookings,
            'bookings_change' => $bookings_change,
            'total_revenue' => $total_revenue,
            'prev_revenue' => $prev_revenue,
            'revenue_change' => $revenue_change,
            'occupancy_rate' => $occupancy_rate,
            'occupancy_change' => $occupancy_change,
            'avg_daily_rate' => $avg_daily_rate,
            'adr_change' => $adr_change,
            'revpar' => $revpar,
            'revpar_change' => $revpar_change,
            'cancellation_rate' => $cancellation_rate,
            'prev_cancellation_rate' => $prev_cancellation_rate,
            'cancellation_change' => $cancellation_change,
            'avg_stay_length' => round($avg_stay_length, 1),
            'prev_avg_stay' => round($prev_avg_stay, 1),
            'stay_change' => $stay_change
        );
    }

    /**
     * Render recent activity
     */
    private function render_recent_activity() {
        $recent_bookings = BookInn_Database_Manager::get_results(
            "SELECT b.*, g.first_name as guest_name, g.email as guest_email, r.room_number
             FROM {bookings} b
             LEFT JOIN {guests} g ON b.guest_id = g.id
             LEFT JOIN {rooms} r ON b.room_id = r.id
             ORDER BY b.created_at DESC
             LIMIT 5"
        );

        if (empty($recent_bookings)) {
            echo '<p class="bookinn-no-data">' . __('No recent activity', 'bookinn') . '</p>';
            return;
        }

        foreach ($recent_bookings as $booking) {
            $guest_name = $booking->guest_name;
            $status_class = 'bookinn-status-' . $booking->status;
            ?>
            <div class="bookinn-activity-item">
                <div class="bookinn-activity-icon">
                    <i class="fa-solid fa-calendar-check bookinn-fa"></i>
                </div>
                <div class="bookinn-activity-content">
                    <p class="bookinn-activity-title">
                        <?php printf(__('New booking by %s', 'bookinn'), esc_html($guest_name)); ?>
                    </p>
                    <p class="bookinn-activity-details">
                        <?php printf(__('Room %s • %s to %s', 'bookinn'),
                            esc_html($booking->room_number),
                            esc_html(date('M j', strtotime($booking->check_in_date))),
                            esc_html(date('M j', strtotime($booking->check_out_date)))
                        ); ?>
                    </p>
                    <span class="bookinn-activity-status <?php echo esc_attr($status_class); ?>">
                        <?php echo esc_html(ucfirst($booking->status)); ?>
                    </span>
                </div>
                <div class="bookinn-activity-time">
                    <?php echo esc_html(human_time_diff(strtotime($booking->created_at), current_time('timestamp'))); ?> <?php _e('ago', 'bookinn'); ?>
                </div>
            </div>
            <?php
        }
    }

    /**
     * Render Bookings Tab with Sub-tabs
     */
    private function render_bookings_tab() {
        ?>
        <div class="bookinn-bookings-container">
            <!-- Sub-tabs Navigation -->
            <div class="bookinn-sub-tabs-nav">
                <a href="#booking-management" class="bookinn-sub-tab-link active" data-subtab="booking-management">
                    <i class="fa-solid fa-calendar-check bookinn-fa"></i>
                    <?php _e('Booking Management', 'bookinn'); ?>
                </a>
                <a href="#guest-registry" class="bookinn-sub-tab-link" data-subtab="guest-registry">
                    <i class="fa-solid fa-users bookinn-fa"></i>
                    <?php _e('Guest Registry', 'bookinn'); ?>
                </a>
            </div>

            <!-- Sub-tabs Content -->
            <div class="bookinn-sub-tabs-content">
                <!-- Booking Management Sub-tab -->
                <div class="bookinn-sub-tab-panel active" id="booking-management">
                    <?php $this->render_booking_management_subtab(); ?>
                </div>

                <!-- Guest Registry Sub-tab -->
                <div class="bookinn-sub-tab-panel" id="guest-registry">
                    <?php $this->render_guest_registry_subtab(); ?>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render Booking Management Sub-tab (existing content)
     */
    private function render_booking_management_subtab() {
        ?>
        <!-- Booking Actions Header -->
        <div class="bookinn-section-header">
            <h3><?php _e('Bookings Management', 'bookinn'); ?></h3>
            <div class="bookinn-section-actions">
                <button class="bookinn-btn bookinn-btn-primary" id="bookinn-add-booking">
                    <i class="fa-solid fa-plus bookinn-fa"></i>
                    <?php _e('New Booking', 'bookinn'); ?>
                </button>
                <button class="bookinn-btn bookinn-btn-secondary" id="bookinn-booking-filters">
                    <i class="fa-solid fa-filter bookinn-fa"></i>
                    <?php _e('Filters', 'bookinn'); ?>
                </button>
                <button class="bookinn-btn bookinn-btn-secondary" id="bookinn-refresh-bookings">
                    <i class="fa-solid fa-rotate bookinn-fa"></i>
                    <?php _e('Refresh', 'bookinn'); ?>
                </button>
            </div>
        </div>

            <!-- Booking Filters (Hidden by default) -->
            <div id="bookinn-list-filters" class="bookinn-list-filters" style="display: none;">
                <div class="bookinn-form-grid">
                    <div class="bookinn-form-group">
                        <label for="filter-status"><?php _e('Status', 'bookinn'); ?></label>
                        <select id="filter-status" class="bookinn-select">
                            <option value=""><?php _e('All Statuses', 'bookinn'); ?></option>
                            <option value="confirmed"><?php _e('Confirmed', 'bookinn'); ?></option>
                            <option value="pending"><?php _e('Pending', 'bookinn'); ?></option>
                            <option value="cancelled"><?php _e('Cancelled', 'bookinn'); ?></option>
                            <option value="checked_in"><?php _e('Checked In', 'bookinn'); ?></option>
                            <option value="checked_out"><?php _e('Checked Out', 'bookinn'); ?></option>
                        </select>
                    </div>
                    <div class="bookinn-form-group">
                        <label for="filter-date-from"><?php _e('From Date', 'bookinn'); ?></label>
                        <input type="date" id="filter-date-from" class="bookinn-input">
                    </div>
                    <div class="bookinn-form-group">
                        <label for="filter-date-to"><?php _e('To Date', 'bookinn'); ?></label>
                        <input type="date" id="filter-date-to" class="bookinn-input">
                    </div>
                    <div class="bookinn-form-group">
                        <label for="filter-room"><?php _e('Room', 'bookinn'); ?></label>
                        <select id="filter-room" class="bookinn-select">
                            <option value=""><?php _e('All Rooms', 'bookinn'); ?></option>
                            <!-- Room options will be populated dynamically -->
                        </select>
                    </div>
                </div>
                <div class="bookinn-filter-actions">
                    <button id="apply-filters" class="bookinn-btn bookinn-btn-primary">
                        <i class="fa-solid fa-filter bookinn-fa"></i>
                        <?php _e('Apply Filters', 'bookinn'); ?>
                    </button>
                    <button id="clear-filters" class="bookinn-btn bookinn-btn-secondary">
                        <i class="fa-solid fa-rotate bookinn-fa"></i>
                        <?php _e('Clear Filters', 'bookinn'); ?>
                    </button>
                </div>
            </div>

            <!-- Bookings List -->
            <div class="bookinn-bookings-list">
                <div class="bookinn-list-filters" id="bookinn-list-filters" style="display: none;">
                    <div class="bookinn-filter-row">
                        <select id="filter-status" class="bookinn-select">
                            <option value=""><?php _e('All Statuses', 'bookinn'); ?></option>
                            <option value="pending"><?php _e('Pending', 'bookinn'); ?></option>
                            <option value="confirmed"><?php _e('Confirmed', 'bookinn'); ?></option>
                            <option value="checked_in"><?php _e('Checked In', 'bookinn'); ?></option>
                            <option value="checked_out"><?php _e('Checked Out', 'bookinn'); ?></option>
                            <option value="cancelled"><?php _e('Cancelled', 'bookinn'); ?></option>
                        </select>

                        <input type="date" id="filter-date-from" class="bookinn-input" placeholder="<?php _e('From Date', 'bookinn'); ?>">
                        <input type="date" id="filter-date-to" class="bookinn-input" placeholder="<?php _e('To Date', 'bookinn'); ?>">

                        <select id="filter-room" class="bookinn-select">
                            <option value=""><?php _e('All Rooms', 'bookinn'); ?></option>
                            <!-- Rooms will be loaded via AJAX -->
                        </select>

                        <button class="bookinn-btn bookinn-btn-primary" id="apply-filters">
                            <?php _e('Apply', 'bookinn'); ?>
                        </button>
                    </div>
                </div>

                <div class="bookinn-table-container">
                    <table class="bookinn-table" id="bookinn-bookings-table">
                        <thead>
                            <tr>
                                <th><?php _e('Booking ID', 'bookinn'); ?></th>
                                <th><?php _e('Guest', 'bookinn'); ?></th>
                                <th><?php _e('Room', 'bookinn'); ?></th>
                                <th><?php _e('Check-in', 'bookinn'); ?></th>
                                <th><?php _e('Check-out', 'bookinn'); ?></th>
                                <th><?php _e('Status', 'bookinn'); ?></th>
                                <th><?php _e('Total', 'bookinn'); ?></th>
                                <th><?php _e('Actions', 'bookinn'); ?></th>
                            </tr>
                        </thead>
                        <tbody id="bookings-table-body">
                            <?php $this->render_bookings_table_rows(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php
    }

    /**
     * Render Guest Registry Sub-tab
     */
    private function render_guest_registry_subtab() {
        ?>
        <!-- Guest Registry Actions Header -->
        <div class="bookinn-section-header">
            <h3><?php _e('Guest Registry', 'bookinn'); ?></h3>
            <div class="bookinn-section-actions">
                <button class="bookinn-btn bookinn-btn-primary" id="bookinn-add-guest">
                    <i class="fa-solid fa-user-plus bookinn-fa"></i>
                    <?php _e('New Guest', 'bookinn'); ?>
                </button>
                <button class="bookinn-btn bookinn-btn-secondary" id="bookinn-guest-search">
                    <i class="fa-solid fa-search bookinn-fa"></i>
                    <?php _e('Search', 'bookinn'); ?>
                </button>
                <button class="bookinn-btn bookinn-btn-secondary" id="bookinn-refresh-guests">
                    <i class="fa-solid fa-rotate bookinn-fa"></i>
                    <?php _e('Refresh', 'bookinn'); ?>
                </button>
            </div>
        </div>

        <!-- Guest Search Filters (Hidden by default) -->
        <div id="bookinn-guest-filters" class="bookinn-list-filters" style="display: none;">
            <div class="bookinn-form-grid">
                <div class="bookinn-form-group">
                    <label for="guest-search-name"><?php _e('Name', 'bookinn'); ?></label>
                    <input type="text" id="guest-search-name" class="bookinn-input" placeholder="<?php _e('Search by name...', 'bookinn'); ?>">
                </div>
                <div class="bookinn-form-group">
                    <label for="guest-search-email"><?php _e('Email', 'bookinn'); ?></label>
                    <input type="email" id="guest-search-email" class="bookinn-input" placeholder="<?php _e('Search by email...', 'bookinn'); ?>">
                </div>
                <div class="bookinn-form-group">
                    <label for="guest-search-phone"><?php _e('Phone', 'bookinn'); ?></label>
                    <input type="text" id="guest-search-phone" class="bookinn-input" placeholder="<?php _e('Search by phone...', 'bookinn'); ?>">
                </div>
                <div class="bookinn-form-group">
                    <label for="guest-search-country"><?php _e('Country', 'bookinn'); ?></label>
                    <input type="text" id="guest-search-country" class="bookinn-input" placeholder="<?php _e('Search by country...', 'bookinn'); ?>">
                </div>
            </div>
            <div class="bookinn-filter-actions">
                <button id="apply-guest-filters" class="bookinn-btn bookinn-btn-primary">
                    <i class="fa-solid fa-search bookinn-fa"></i>
                    <?php _e('Search Guests', 'bookinn'); ?>
                </button>
                <button id="clear-guest-filters" class="bookinn-btn bookinn-btn-secondary">
                    <i class="fa-solid fa-times bookinn-fa"></i>
                    <?php _e('Clear Search', 'bookinn'); ?>
                </button>
            </div>
        </div>

        <!-- Guests Table -->
        <div class="bookinn-guests-list">
            <div class="bookinn-table-container">
                <table class="bookinn-table" id="bookinn-guests-table">
                    <thead>
                        <tr>
                            <th style="width: 40px;">
                                <input type="checkbox" id="select-all-guests" title="<?php _e('Select All', 'bookinn'); ?>">
                            </th>
                            <th><?php _e('Name', 'bookinn'); ?></th>
                            <th><?php _e('Email', 'bookinn'); ?></th>
                            <th><?php _e('Phone', 'bookinn'); ?></th>
                            <th><?php _e('Country', 'bookinn'); ?></th>
                            <th><?php _e('Created', 'bookinn'); ?></th>
                            <th><?php _e('Actions', 'bookinn'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="guests-table-body">
                        <?php $this->render_guests_table_rows(); ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php
    }

    /**
     * Render guests table rows
     */
    private function render_guests_table_rows() {
        $guests = BookInn_Database_Manager::get_results("
            SELECT g.*,
                   COUNT(b.id) as total_bookings,
                   MAX(b.created_at) as last_booking_date
            FROM {guests} g
            LEFT JOIN {bookings} b ON g.id = b.guest_id
            GROUP BY g.id
            ORDER BY g.created_at DESC
            LIMIT 50
        ");

        if (empty($guests)) {
            echo '<tr><td colspan="7" class="bookinn-no-data">' . __('No guests found.', 'bookinn') . '</td></tr>';
            return;
        }

        foreach ($guests as $guest) {
            $full_name = trim($guest->first_name . ' ' . $guest->last_name);
            $id_type_display = $guest->id_type ? ucfirst(str_replace('_', ' ', $guest->id_type)) : '-';
            $date_of_birth = $guest->date_of_birth ? date('M j, Y', strtotime($guest->date_of_birth)) : '-';
            $created_date = date('M j, Y', strtotime($guest->created_at));

            echo '<tr data-guest-id="' . esc_attr($guest->id) . '">';
            echo '<td><input type="checkbox" class="guest-checkbox" value="' . esc_attr($guest->id) . '"></td>';
            echo '<td>';
            echo '<div class="bookinn-guest-info">';
            echo '<strong>' . esc_html($full_name) . '</strong>';
            if ($guest->total_bookings > 0) {
                echo '<br><small class="bookinn-text-muted">' .
                     sprintf(__('%d bookings', 'bookinn'), $guest->total_bookings) . '</small>';
            }
            echo '</div>';
            echo '</td>';
            echo '<td>' . esc_html($guest->email) . '</td>';
            echo '<td>' . esc_html($guest->phone ?: '-') . '</td>';
            echo '<td>' . esc_html($guest->country ?: '-') . '</td>';
            echo '<td>' . esc_html($created_date) . '</td>';
            echo '<td>';
            echo '<div class="bookinn-actions">';
            echo '<button class="bookinn-btn bookinn-btn-sm bookinn-btn-secondary bookinn-edit-guest" ';
            echo 'data-guest-id="' . esc_attr($guest->id) . '" title="' . __('Edit Guest', 'bookinn') . '">';
            echo '<i class="fa-solid fa-edit bookinn-fa"></i>';
            echo '</button>';
            echo '<button class="bookinn-btn bookinn-btn-sm bookinn-btn-danger bookinn-delete-guest" ';
            echo 'data-guest-id="' . esc_attr($guest->id) . '" title="' . __('Delete Guest', 'bookinn') . '">';
            echo '<i class="fa-solid fa-trash bookinn-fa"></i>';
            echo '</button>';
            echo '</div>';
            echo '</td>';
            echo '</tr>';
        }
    }

    /**
     * Render bookings table rows
     */
    private function render_bookings_table_rows() {
        $bookings = BookInn_Database_Manager::get_results("
            SELECT b.*, g.first_name as guest_name, g.email as guest_email, r.room_number, rt.name as room_type
            FROM {bookings} b
            LEFT JOIN {guests} g ON b.guest_id = g.id
            LEFT JOIN {rooms} r ON b.room_id = r.id
            LEFT JOIN {room_types} rt ON r.room_type_id = rt.id
            ORDER BY b.created_at DESC
            LIMIT 20
        ");

        if (empty($bookings)) {
            echo '<tr><td colspan="8" class="bookinn-no-data">' . __('No bookings found', 'bookinn') . '</td></tr>';
            return;
        }

        foreach ($bookings as $booking) {
            $guest_name = $booking->guest_name;
            $status_class = 'bookinn-status-' . $booking->status;
            ?>
            <tr data-booking-id="<?php echo esc_attr($booking->id); ?>">
                <td>
                    <strong>#<?php echo esc_html($booking->id); ?></strong>
                </td>
                <td>
                    <div class="bookinn-guest-info">
                        <strong><?php echo esc_html($guest_name); ?></strong>
                        <small><?php echo esc_html($booking->guest_email); ?></small>
                    </div>
                </td>
                <td>
                    <div class="bookinn-room-info">
                        <strong><?php echo esc_html($booking->room_number); ?></strong>
                        <small><?php echo esc_html(ucfirst($booking->room_type)); ?></small>
                    </div>
                </td>
                <td><?php echo esc_html(date('M j, Y', strtotime($booking->check_in_date))); ?></td>
                <td><?php echo esc_html(date('M j, Y', strtotime($booking->check_out_date))); ?></td>
                <td>
                    <span class="bookinn-status-badge <?php echo esc_attr($status_class); ?>">
                        <?php echo esc_html(ucfirst(str_replace('_', ' ', $booking->status))); ?>
                    </span>
                </td>
                <td>
                    <strong>€<?php echo esc_html(number_format($booking->total_amount, 2)); ?></strong>
                </td>
                <td>
                    <div class="bookinn-actions">
                        <button class="bookinn-btn bookinn-btn-sm bookinn-btn-secondary bookinn-view-booking" data-booking-id="<?php echo esc_attr($booking->id); ?>">
                            <?php _e('View', 'bookinn'); ?>
                        </button>
                         <button class="bookinn-btn bookinn-btn-sm bookinn-btn-secondary bookinn-edit-booking" data-booking-id="<?php echo esc_attr($booking->id); ?>">
                            <?php _e('Edit', 'bookinn'); ?>
                        </button>
                        <?php if ($booking->status === 'pending'): ?>
                        <button class="bookinn-btn bookinn-btn-sm bookinn-btn-primary bookinn-confirm-booking" data-booking-id="<?php echo esc_attr($booking->id); ?>">
                            <?php _e('Confirm', 'bookinn'); ?>
                        </button>
                        <?php endif; ?>
                    </div>
                </td>
            </tr>
            <?php
        }
    }

    /**
     * Render Rooms Tab
     */
    private function render_rooms_tab() {
        ?>
        <div class="bookinn-rooms-container">
            <!-- Room Sub-tabs -->
            <div class="bookinn-sub-tabs">
                <div class="bookinn-sub-tab-nav">
                    <a href="#rooms-list" class="bookinn-sub-tab-link active" data-tab="rooms-list">
                        <?php _e('Rooms List', 'bookinn'); ?>
                    </a>
                    <a href="#room-types" class="bookinn-sub-tab-link" data-tab="room-types">
                        <?php _e('Room Types', 'bookinn'); ?>
                    </a>
                    <a href="#rate-plans" class="bookinn-sub-tab-link" data-tab="rate-plans">
                        <?php _e('Rate Plans', 'bookinn'); ?>
                    </a>
                </div>

                <!-- Rooms List Sub-tab -->
                <div class="bookinn-sub-tab-panel active" id="rooms-list">
                    <!-- Rooms Header -->
                    <div class="bookinn-section-header">
                        <h3><?php _e('Rooms Management', 'bookinn'); ?></h3>
                        <div class="bookinn-section-actions">
                            <button class="bookinn-btn bookinn-btn-primary" id="bookinn-add-room">
                                <i class="fa-solid fa-plus bookinn-fa"></i>
                                <?php _e('Add Room', 'bookinn'); ?>
                            </button>
                            <button class="bookinn-btn bookinn-btn-secondary" id="bookinn-room-filters">
                                <i class="fa-solid fa-filter bookinn-fa"></i>
                                <?php _e('Filters', 'bookinn'); ?>
                            </button>
                            <button class="bookinn-btn bookinn-btn-secondary" id="bookinn-refresh-rooms">
                                <i class="fa-solid fa-rotate bookinn-fa"></i>
                                <?php _e('Refresh', 'bookinn'); ?>
                            </button>
                        </div>
                    </div>

                    <!-- Room Filters (Hidden by default) -->
                    <div id="bookinn-room-list-filters" class="bookinn-list-filters" style="display: none;">
                        <div class="bookinn-form-grid">
                            <div class="bookinn-form-group">
                                <label for="rooms-status-filter"><?php _e('Status', 'bookinn'); ?></label>
                                <select id="rooms-status-filter" class="bookinn-select">
                                    <option value=""><?php _e('All Status', 'bookinn'); ?></option>
                                    <option value="available"><?php _e('Available', 'bookinn'); ?></option>
                                    <option value="occupied"><?php _e('Occupied', 'bookinn'); ?></option>
                                    <option value="maintenance"><?php _e('Maintenance', 'bookinn'); ?></option>
                                    <option value="cleaning"><?php _e('Cleaning', 'bookinn'); ?></option>
                                    <option value="out_of_order"><?php _e('Out of Order', 'bookinn'); ?></option>
                                </select>
                            </div>
                            <div class="bookinn-form-group">
                                <label for="rooms-number-filter"><?php _e('Room Number', 'bookinn'); ?></label>
                                <input type="text" id="rooms-number-filter" class="bookinn-input" placeholder="<?php _e('Search by room number', 'bookinn'); ?>">
                            </div>
                            <div class="bookinn-form-group">
                                <label for="rooms-type-filter"><?php _e('Room Type', 'bookinn'); ?></label>
                                <select id="rooms-type-filter" class="bookinn-select">
                                    <option value=""><?php _e('All Types', 'bookinn'); ?></option>
                                    <!-- Room types will be loaded via AJAX -->
                                </select>
                            </div>
                        </div>
                        <div class="bookinn-filter-actions">
                            <button id="apply-room-filters" class="bookinn-btn bookinn-btn-primary">
                                <i class="fa-solid fa-filter bookinn-fa"></i>
                                <?php _e('Apply Filters', 'bookinn'); ?>
                            </button>
                            <button id="clear-room-filters" class="bookinn-btn bookinn-btn-secondary">
                                <i class="fa-solid fa-rotate bookinn-fa"></i>
                                <?php _e('Clear Filters', 'bookinn'); ?>
                            </button>
                        </div>
                    </div>

                    <!-- Rooms Table -->
                    <div class="bookinn-table-container">
                        <table class="bookinn-table" id="bookinn-rooms-table">
                            <thead>
                                <tr>
                                    <th><?php _e('Room Number', 'bookinn'); ?></th>
                                    <th><?php _e('Type', 'bookinn'); ?></th>
                                    <th><?php _e('Floor', 'bookinn'); ?></th>
                                    <th><?php _e('Status', 'bookinn'); ?></th>
                                    <th><?php _e('Active', 'bookinn'); ?></th>
                                    <th><?php _e('Price/Night', 'bookinn'); ?></th>
                                    <th><?php _e('Actions', 'bookinn'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $this->render_rooms_table_rows(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Rate Plans Sub-tab -->
                <div class="bookinn-sub-tab-panel" id="rate-plans">
                    <div class="bookinn-section-header">
                        <h3><?php _e('Rate Plans Management', 'bookinn'); ?></h3>
                        <div class="bookinn-section-actions">
                            <button class="bookinn-btn bookinn-btn-primary" id="bookinn-add-rate-plan">
                                <i class="fa-solid fa-plus bookinn-fa"></i>
                                <?php _e('Add Rate Plan', 'bookinn'); ?>
                            </button>
                            <button class="bookinn-btn bookinn-btn-secondary" id="bookinn-rate-plan-filters">
                                <i class="fa-solid fa-filter bookinn-fa"></i>
                                <?php _e('Filters', 'bookinn'); ?>
                            </button>
                            <button class="bookinn-btn bookinn-btn-secondary" id="bookinn-refresh-rate-plans">
                                <i class="fa-solid fa-rotate bookinn-fa"></i>
                                <?php _e('Refresh', 'bookinn'); ?>
                            </button>
                        </div>
                    </div>

                    <!-- Rate Plans Filters -->
                    <div class="bookinn-filters-container" id="rate-plans-filters" style="display: none;">
                        <div class="bookinn-filters-row">
                            <div class="bookinn-filter-group">
                                <label for="rate-plan-room-type-filter"><?php _e('Room Type', 'bookinn'); ?></label>
                                <select id="rate-plan-room-type-filter" class="bookinn-select">
                                    <option value=""><?php _e('All Room Types', 'bookinn'); ?></option>
                                    <?php $this->render_room_type_options(); ?>
                                </select>
                            </div>
                            <div class="bookinn-filter-group">
                                <label for="rate-plan-date-from"><?php _e('Date From', 'bookinn'); ?></label>
                                <input type="date" id="rate-plan-date-from" class="bookinn-input">
                            </div>
                            <div class="bookinn-filter-group">
                                <label for="rate-plan-date-to"><?php _e('Date To', 'bookinn'); ?></label>
                                <input type="date" id="rate-plan-date-to" class="bookinn-input">
                            </div>
                            <div class="bookinn-filter-group">
                                <label for="rate-plan-status-filter"><?php _e('Status', 'bookinn'); ?></label>
                                <select id="rate-plan-status-filter" class="bookinn-select">
                                    <option value=""><?php _e('All Status', 'bookinn'); ?></option>
                                    <option value="1"><?php _e('Active', 'bookinn'); ?></option>
                                    <option value="0"><?php _e('Inactive', 'bookinn'); ?></option>
                                </select>
                            </div>
                        </div>
                        <div class="bookinn-filters-actions">
                            <button id="apply-rate-plan-filters" class="bookinn-btn bookinn-btn-primary">
                                <i class="fa-solid fa-search bookinn-fa"></i>
                                <?php _e('Apply Filters', 'bookinn'); ?>
                            </button>
                            <button id="clear-rate-plan-filters" class="bookinn-btn bookinn-btn-secondary">
                                <i class="fa-solid fa-rotate bookinn-fa"></i>
                                <?php _e('Clear Filters', 'bookinn'); ?>
                            </button>
                        </div>
                    </div>

                    <!-- Bulk Actions Bar -->
                    <div class="bookinn-rate-plans-bulk-actions bulk-actions" id="rate-plans-bulk-actions" style="display: none;">
                        <div class="bookinn-bulk-actions-info">
                            <span id="selected-count">0</span> <?php _e('items selected', 'bookinn'); ?>
                        </div>
                        <div class="bookinn-bulk-actions-buttons">
                            <button class="bookinn-btn bookinn-btn-sm bookinn-btn-success bulk-action-btn" data-action="activate">
                                <?php _e('Activate', 'bookinn'); ?>
                            </button>
                            <button class="bookinn-btn bookinn-btn-sm bookinn-btn-secondary bulk-action-btn" data-action="deactivate">
                                <?php _e('Deactivate', 'bookinn'); ?>
                            </button>
                            <button class="bookinn-btn bookinn-btn-sm bookinn-btn-danger bulk-action-btn" data-action="delete">
                                <?php _e('Delete', 'bookinn'); ?>
                            </button>
                        </div>
                    </div>

                    <!-- Rate Plans Table -->
                    <div class="bookinn-table-container">
                        <table class="bookinn-table bookinn-rate-plans-table" id="bookinn-rate-plans-table">
                            <thead>
                                <tr>
                                    <th style="width: 40px;">
                                        <input type="checkbox" id="select-all-rate-plans" title="<?php _e('Select All', 'bookinn'); ?>">
                                    </th>
                                    <th><?php _e('Rate Plan Name', 'bookinn'); ?></th>
                                    <th><?php _e('Room Type', 'bookinn'); ?></th>
                                    <th><?php _e('Date', 'bookinn'); ?></th>
                                    <th><?php _e('Price', 'bookinn'); ?></th>
                                    <th><?php _e('Meal Plan', 'bookinn'); ?></th>
                                    <th><?php _e('Min Stay', 'bookinn'); ?></th>
                                    <th><?php _e('Max Stay', 'bookinn'); ?></th>
                                    <th><?php _e('Weekend', 'bookinn'); ?></th>
                                    <th><?php _e('Status', 'bookinn'); ?></th>
                                    <th><?php _e('Actions', 'bookinn'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $this->render_rate_plans_table_rows(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Room Types Sub-tab -->
                <div class="bookinn-sub-tab-panel" id="room-types">
                    <!-- Room Types Header -->
                    <div class="bookinn-section-header">
                        <h3><?php _e('Room Types Management', 'bookinn'); ?></h3>
                        <div class="bookinn-section-actions">
                            <button class="bookinn-btn bookinn-btn-primary" id="bookinn-add-room-type">
                                <?php _e('Add Room Type', 'bookinn'); ?>
                            </button>
                        </div>
                    </div>

                    <!-- Room Types Table -->
                    <div class="bookinn-table-container">
                        <table class="bookinn-table" id="bookinn-room-types-table">
                            <thead>
                                <tr>
                                    <th><?php _e('Type Name', 'bookinn'); ?></th>
                                    <th><?php _e('Description', 'bookinn'); ?></th>
                                    <th><?php _e('Base Price', 'bookinn'); ?></th>
                                    <th><?php _e('Max Guests', 'bookinn'); ?></th>
                                    <th><?php _e('Actions', 'bookinn'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $this->render_room_types_table_rows(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Room Editor Modal - Enhanced Accessibility -->
            <div class="bookinn-modal" 
                 id="bookinn-room-modal" 
                 role="dialog" 
                 aria-labelledby="room-modal-title" 
                 aria-hidden="true"
                 tabindex="-1"
                 style="display: none;">
                <div class="bookinn-modal-content" role="document">
                    <div class="bookinn-modal-header">
                        <h4 id="room-modal-title" tabindex="0"><?php _e('Edit Room', 'bookinn'); ?></h4>
                        <button class="bookinn-close-modal" 
                                id="close-room-modal"
                                aria-label="<?php _e('Close modal', 'bookinn'); ?>"
                                type="button">×</button>
                    </div>
                    <div class="bookinn-modal-body">
                        <form id="room-form" aria-labelledby="room-modal-title">
                            <div class="bookinn-form-grid">
                                <div class="bookinn-form-group">
                                    <label for="room-number"><?php _e('Room Number', 'bookinn'); ?></label>
                                    <input type="text" 
                                           id="room-number" 
                                           class="bookinn-input" 
                                           aria-describedby="room-number-help"
                                           required>
                                    <small id="room-number-help" class="bookinn-help-text">
                                        <?php _e('Enter the room number (e.g., 101, 202)', 'bookinn'); ?>
                                    </small>
                                </div>
                                <div class="bookinn-form-group">
                                    <label for="room-type"><?php _e('Room Type', 'bookinn'); ?></label>
                                    <select id="room-type" 
                                            class="bookinn-select" 
                                            aria-describedby="room-type-help"
                                            required>
                                        <?php $this->render_room_type_options(); ?>
                                    </select>
                                    <small id="room-type-help" class="bookinn-help-text">
                                        <?php _e('Select the room category', 'bookinn'); ?>
                                    </small>
                                </div>
                                <div class="bookinn-form-group">
                                    <label for="room-floor"><?php _e('Floor', 'bookinn'); ?></label>
                                    <input type="number" 
                                           id="room-floor" 
                                           class="bookinn-input" 
                                           aria-describedby="room-floor-help"
                                           min="0" 
                                           max="50">
                                    <small id="room-floor-help" class="bookinn-help-text">
                                        <?php _e('Floor number (0 for ground floor)', 'bookinn'); ?>
                                    </small>
                                </div>
                                <div class="bookinn-form-group">
                                    <label><?php _e('Status', 'bookinn'); ?></label>
                                    <select id="room-status" class="bookinn-select">
                                        <option value="available"><?php _e('Available', 'bookinn'); ?></option>
                                        <option value="occupied"><?php _e('Occupied', 'bookinn'); ?></option>
                                        <option value="maintenance"><?php _e('Maintenance', 'bookinn'); ?></option>
                                        <option value="cleaning"><?php _e('Cleaning', 'bookinn'); ?></option>
                                        <option value="out_of_order"><?php _e('Out of Order', 'bookinn'); ?></option>
                                    </select>
                                </div>
                            </div>
                            <div class="bookinn-form-group">
                                <label><?php _e('Room Name (Optional)', 'bookinn'); ?></label>
                                <input type="text" id="room-name" class="bookinn-input">
                            </div>
                            <input type="hidden" id="room-id" value="">
                        </form>
                    </div>
                    <div class="bookinn-modal-footer">
                        <button class="bookinn-btn bookinn-btn-secondary bookinn-close-modal"><?php _e('Cancel', 'bookinn'); ?></button>
                        <button class="bookinn-btn bookinn-btn-primary" id="save-room"><?php _e('Save Room', 'bookinn'); ?></button>
                    </div>
                </div>
            </div>

            <!-- Room Type Editor Modal - Enhanced Accessibility -->
            <div class="bookinn-modal" 
                 id="bookinn-room-type-modal" 
                 role="dialog" 
                 aria-labelledby="room-type-modal-title" 
                 aria-hidden="true"
                 tabindex="-1"
                 style="display: none;">
                <div class="bookinn-modal-content" role="document">
                    <div class="bookinn-modal-header">
                        <h4 id="room-type-modal-title" tabindex="0"><?php _e('Edit Room Type', 'bookinn'); ?></h4>
                        <button class="bookinn-close-modal" 
                                id="close-room-type-modal"
                                aria-label="<?php _e('Close modal', 'bookinn'); ?>"
                                type="button">×</button>
                    </div>
                    <div class="bookinn-modal-body">
                        <form id="room-type-form" aria-labelledby="room-type-modal-title">
                            <div class="bookinn-form-grid">
                                <div class="bookinn-form-group">
                                    <label for="room-type-name"><?php _e('Type Name', 'bookinn'); ?></label>
                                    <input type="text" 
                                           id="room-type-name" 
                                           class="bookinn-input" 
                                           aria-describedby="room-type-name-help"
                                           required>
                                    <small id="room-type-name-help" class="bookinn-help-text">
                                        <?php _e('Name of the room category (e.g., Standard, Deluxe)', 'bookinn'); ?>
                                    </small>
                                </div>
                                <div class="bookinn-form-group">
                                    <label for="room-type-price"><?php _e('Base Price (€)', 'bookinn'); ?></label>
                                    <input type="number" 
                                           id="room-type-price" 
                                           class="bookinn-input" 
                                           aria-describedby="room-type-price-help"
                                           min="0" 
                                           step="0.01" 
                                           required>
                                    <small id="room-type-price-help" class="bookinn-help-text">
                                        <?php _e('Base price per night in euros', 'bookinn'); ?>
                                    </small>
                                </div>
                                <div class="bookinn-form-group">
                                    <label for="room-type-max-guests"><?php _e('Max Guests', 'bookinn'); ?></label>
                                    <input type="number" 
                                           id="room-type-max-guests" 
                                           class="bookinn-input" 
                                           aria-describedby="room-type-max-guests-help"
                                           min="1" 
                                           max="10" 
                                           required>
                                    <small id="room-type-max-guests-help" class="bookinn-help-text">
                                        <?php _e('Maximum number of guests allowed', 'bookinn'); ?>
                                    </small>
                                </div>
                            </div>
                            <div class="bookinn-form-group">
                                <label for="room-type-description"><?php _e('Description', 'bookinn'); ?></label>
                                <textarea id="room-type-description" class="bookinn-textarea" rows="3"></textarea>
                            </div>
                            <input type="hidden" id="room-type-id" value="">
                        </form>
                    </div>
                    <div class="bookinn-modal-footer">
                        <button class="bookinn-btn bookinn-btn-secondary bookinn-close-modal"><?php _e('Cancel', 'bookinn'); ?></button>
                        <button class="bookinn-btn bookinn-btn-primary" id="save-room-type"><?php _e('Save Room Type', 'bookinn'); ?></button>
                    </div>
                </div>
            </div>

            <!-- New Booking Modal -->
         <!--   <div class="bookinn-modal" 
                 id="bookinn-new-booking-modal" 
                 role="dialog" 
                 aria-labelledby="new-booking-modal-title" 
                 aria-hidden="true"
                 tabindex="-1"
                 style="display: none;">
                <div class="bookinn-modal-content bookinn-modal-lg" role="document">
                    <div class="bookinn-modal-header">
                        <h4 id="new-booking-modal-title" tabindex="0"><?php _e('New Booking', 'bookinn'); ?></h4>
                        <button class="bookinn-close-modal" 
                                aria-label="<?php _e('Close modal', 'bookinn'); ?>"
                                type="button">×</button>
                    </div>
                    <div class="bookinn-modal-body">
                        <form id="new-booking-form" aria-labelledby="new-booking-modal-title">
                        
                            <div class="bookinn-card">
                                <div class="bookinn-card-header">
                                    <h5><i class="fas fa-calendar"></i> <?php _e('Booking Details', 'bookinn'); ?></h5>
                                </div>
                                <div class="bookinn-card-body">
                                
                                    <div class="bookinn-form-section">
                                        <div class="bookinn-form-grid">
                                            <div class="bookinn-form-group">
                                                <label for="new-booking-checkin"><?php _e('Check-in Date', 'bookinn'); ?> *</label>
                                                <input type="date" id="new-booking-checkin" name="check_in_date" class="bookinn-input" required>
                                            </div>
                                            <div class="bookinn-form-group">
                                                <label for="new-booking-checkout"><?php _e('Check-out Date', 'bookinn'); ?> *</label>
                                                <input type="date" id="new-booking-checkout" name="check_out_date" class="bookinn-input" required>
                                            </div>
                                            <div class="bookinn-form-group">
                                                <label for="new-booking-adults"><?php _e('Adults', 'bookinn'); ?> *</label>
                                                <select id="new-booking-adults" name="adults" class="bookinn-select" required>
                                                    <option value="1">1</option>
                                                    <option value="2" selected>2</option>
                                                    <option value="3">3</option>
                                                    <option value="4">4</option>
                                                    <option value="5">5</option>
                                                    <option value="6">6</option>
                                                </select>
                                            </div>
                                            <div class="bookinn-form-group">
                                                <label for="new-booking-children"><?php _e('Children', 'bookinn'); ?></label>
                                                <select id="new-booking-children" name="children" class="bookinn-select">
                                                    <option value="0" selected>0</option>
                                                    <option value="1">1</option>
                                                    <option value="2">2</option>
                                                    <option value="3">3</option>
                                                    <option value="4">4</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                  
                                    <div class="bookinn-form-section">
                                        <div class="bookinn-form-grid">
                                            <div class="bookinn-form-group">
                                                <label for="new-guest-first-name"><?php _e('First Name', 'bookinn'); ?> *</label>
                                                <input type="text" id="new-guest-first-name" name="guest_first_name" class="bookinn-input" required>
                                            </div>
                                            <div class="bookinn-form-group">
                                                <label for="new-guest-last-name"><?php _e('Last Name', 'bookinn'); ?> *</label>
                                                <input type="text" id="new-guest-last-name" name="guest_last_name" class="bookinn-input" required>
                                            </div>
                                            <div class="bookinn-form-group">
                                                <label for="new-guest-email"><?php _e('Email', 'bookinn'); ?> *</label>
                                                <input type="email" id="new-guest-email" name="guest_email" class="bookinn-input" required>
                                            </div>
                                            <div class="bookinn-form-group">
                                                <label for="new-guest-phone"><?php _e('Phone', 'bookinn'); ?> *</label>
                                                <input type="tel" id="new-guest-phone" name="guest_phone" class="bookinn-input" required>
                                            </div>
                                        </div>
                                    </div>

                                  
                                    <div class="bookinn-form-section">
                                        <div class="bookinn-form-group">
                                            <label for="new-booking-room"><?php _e('Room', 'bookinn'); ?> *</label>
                                            <select id="new-booking-room" name="room_id" class="bookinn-select" required>
                                                <option value=""><?php _e('Select Room', 'bookinn'); ?></option>
                                                
                                            </select>
                                        </div>
                                        <div class="bookinn-form-group full-width">
                                            <label for="new-booking-notes"><?php _e('Special Requests', 'bookinn'); ?></label>
                                            <textarea id="new-booking-notes" name="notes" class="bookinn-textarea" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                           
                            <div class="bookinn-form-section">
                                <h5><i class="fas fa-cogs"></i> <?php _e('Booking Options', 'bookinn'); ?></h5>
                                <div class="bookinn-form-grid">
                                    <div class="bookinn-form-group">
                                        <label for="new-booking-meal-plan"><?php _e('Meal Plan', 'bookinn'); ?></label>
                                        <select id="new-booking-meal-plan" name="meal_plan" class="bookinn-select">
                                            <option value="room_only"><?php _e('Room Only', 'bookinn'); ?></option>
                                            <option value="breakfast"><?php _e('Breakfast Included', 'bookinn'); ?></option>
                                            <option value="half_board"><?php _e('Half Board', 'bookinn'); ?></option>
                                            <option value="full_board"><?php _e('Full Board', 'bookinn'); ?></option>
                                            <option value="all_inclusive"><?php _e('All Inclusive', 'bookinn'); ?></option>
                                        </select>
                                        <small class="bookinn-help-text"><?php _e('Select the meal plan for this booking', 'bookinn'); ?></small>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-booking-cancellation-policy"><?php _e('Cancellation Policy', 'bookinn'); ?></label>
                                        <select id="new-booking-cancellation-policy" name="cancellation_policy" class="bookinn-select">
                                            <option value="flexible"><?php _e('Flexible', 'bookinn'); ?></option>
                                            <option value="semi_flexible"><?php _e('Semi-Flexible', 'bookinn'); ?></option>
                                            <option value="non_refundable"><?php _e('Non-Refundable', 'bookinn'); ?></option>
                                            <option value="custom"><?php _e('Custom', 'bookinn'); ?></option>
                                        </select>
                                        <small class="bookinn-help-text"><?php _e('Cancellation terms for this booking', 'bookinn'); ?></small>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-booking-distribution-channel"><?php _e('Distribution Channel', 'bookinn'); ?></label>
                                        <select id="new-booking-distribution-channel" name="distribution_channel" class="bookinn-select">
                                            <option value="direct"><?php _e('Direct Booking', 'bookinn'); ?></option>
                                            <option value="official_site"><?php _e('Official Website', 'bookinn'); ?></option>
                                            <option value="ota"><?php _e('OTA Platform', 'bookinn'); ?></option>
                                            <option value="corporate"><?php _e('Corporate Booking', 'bookinn'); ?></option>
                                        </select>
                                        <small class="bookinn-help-text"><?php _e('Source of this booking', 'bookinn'); ?></small>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-booking-rate-plan"><?php _e('Rate Plan', 'bookinn'); ?></label>
                                        <select id="new-booking-rate-plan" name="rate_plan_id" class="bookinn-select">
                                            <option value=""><?php _e('Select Rate Plan (Optional)', 'bookinn'); ?></option>
                                           
                                        </select>
                                        <small class="bookinn-help-text"><?php _e('Optional: Link to a specific rate plan', 'bookinn'); ?></small>
                                    </div>
                                </div>
                            </div>


                            <div class="bookinn-form-section">
                                <h5><i class="fas fa-calculator"></i> <?php _e('Pricing Calculation', 'bookinn'); ?></h5>
                                <div class="bookinn-pricing-calculator">
                                    <div class="bookinn-form-grid">
                                        <div class="bookinn-form-group">
                                            <label><?php _e('Base Rate', 'bookinn'); ?></label>
                                            <div class="bookinn-price-display" id="booking-base-rate">€0.00</div>
                                            <small class="bookinn-help-text"><?php _e('Per night base rate', 'bookinn'); ?></small>
                                        </div>
                                        <div class="bookinn-form-group">
                                            <label><?php _e('Nights', 'bookinn'); ?></label>
                                            <div class="bookinn-price-display" id="booking-nights">0</div>
                                            <small class="bookinn-help-text"><?php _e('Number of nights', 'bookinn'); ?></small>
                                        </div>
                                        <div class="bookinn-form-group">
                                            <label><?php _e('Meal Plan Supplement', 'bookinn'); ?></label>
                                            <div class="bookinn-price-display" id="booking-meal-supplement">€0.00</div>
                                            <small class="bookinn-help-text"><?php _e('Additional cost for meal plan', 'bookinn'); ?></small>
                                        </div>
                                        <div class="bookinn-form-group">
                                            <label><?php _e('Subtotal', 'bookinn'); ?></label>
                                            <div class="bookinn-price-display" id="booking-subtotal">€0.00</div>
                                            <small class="bookinn-help-text"><?php _e('Before taxes and fees', 'bookinn'); ?></small>
                                        </div>
                                    </div>
                                    <div class="bookinn-pricing-breakdown" id="booking-pricing-breakdown" style="display: none;">
                                        <h6><?php _e('Pricing Breakdown', 'bookinn'); ?></h6>
                                        <div class="bookinn-breakdown-details"></div>
                                    </div>
                                  </div>
                            </div>

                          
                            <div class="bookinn-card">
                                <div class="bookinn-card-header">
                                    <h5><i class="fas fa-credit-card"></i> <?php _e('Payment & Status', 'bookinn'); ?></h5>
                                </div>
                                <div class="bookinn-card-body">
                                    <div class="bookinn-form-section">
                                        <div class="bookinn-form-grid">
                                            <div class="bookinn-form-group">
                                                <label for="new-booking-total"><?php _e('Total Amount (€)', 'bookinn'); ?> *</label>
                                                <input type="number" id="new-booking-total" name="total_amount" class="bookinn-input" step="0.01" min="0" required>
                                            </div>
                                            <div class="bookinn-form-group">
                                                <label for="new-booking-payment-method"><?php _e('Payment Method', 'bookinn'); ?> *</label>
                                                <select id="new-booking-payment-method" name="payment_method" class="bookinn-select" required>
                                                    <option value=""><?php _e('Select Payment Method', 'bookinn'); ?></option>
                                                    <option value="credit_card"><?php _e('Credit Card', 'bookinn'); ?></option>
                                                    <option value="cash"><?php _e('Cash', 'bookinn'); ?></option>
                                                    <option value="bank_transfer"><?php _e('Bank Transfer', 'bookinn'); ?></option>
                                                </select>
                                            </div>
                                            <div class="bookinn-form-group">
                                                <label for="new-booking-status"><?php _e('Status', 'bookinn'); ?> *</label>
                                                <select id="new-booking-status" name="status" class="bookinn-select" required>
                                                    <option value="pending"><?php _e('Pending', 'bookinn'); ?></option>
                                                    <option value="confirmed" selected><?php _e('Confirmed', 'bookinn'); ?></option>
                                                    <option value="checked_in"><?php _e('Checked In', 'bookinn'); ?></option>
                                                    <option value="checked_out"><?php _e('Checked Out', 'bookinn'); ?></option>
                                                    <option value="cancelled"><?php _e('Cancelled', 'bookinn'); ?></option>
                                                    <option value="no_show"><?php _e('No Show', 'bookinn'); ?></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="bookinn-modal-footer">
                        <button class="bookinn-btn bookinn-btn-secondary bookinn-close-modal"><?php _e('Cancel', 'bookinn'); ?></button>
                        <button class="bookinn-btn bookinn-btn-success" id="save-new-booking"><?php _e('Create Booking', 'bookinn'); ?></button>
                    </div>
                </div>
            </div> -->

            <!-- Guest Management Modal -->
            <div class="bookinn-modal"
                 id="bookinn-guest-modal"
                 role="dialog"
                 aria-labelledby="guest-modal-title"
                 aria-hidden="true"
                 tabindex="-1"
                 style="display: none;">
                <div class="bookinn-modal-container bookinn-modal-wide" role="document">
                    <div class="bookinn-modal-header">
                        <div class="bookinn-modal-title-group">
                            <img src="<?php echo BOOKINN_PLUGIN_URL . 'assets/images/logo_form.png'; ?>" alt="BookInn Logo" class="bookinn-modal-logo">
                            <h4 id="guest-modal-title" tabindex="0"><?php _e('Guest Information', 'bookinn'); ?></h4>
                        </div>
                        <button class="bookinn-close-modal"
                                id="close-guest-modal"
                                aria-label="<?php _e('Close modal', 'bookinn'); ?>"
                                type="button">×</button>
                    </div>
                    <div class="bookinn-modal-body">
                        <form id="guest-form" aria-labelledby="guest-modal-title">

                            <!-- Personal Information Section -->
                            <div class="bookinn-form-section bookinn-card">
                                <h5>
                                    <svg class="bookinn-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M6 21V19C6 17.9391 6.42143 16.9217 7.17157 16.1716C7.92172 15.4214 8.93913 15 10 15H14C15.0609 15 16.0783 15.4214 16.8284 16.1716C17.5786 16.9217 18 17.9391 18 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <?php _e('Personal Information', 'bookinn'); ?>
                                </h5>
                                <div class="bookinn-form-grid bookinn-form-grid-2">
                                    <div class="bookinn-form-group">
                                        <label for="guest-first-name"><?php _e('First Name', 'bookinn'); ?> <span class="bookinn-required">*</span></label>
                                        <input type="text" id="guest-first-name" name="first_name" class="bookinn-input" required>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="guest-last-name"><?php _e('Last Name', 'bookinn'); ?> <span class="bookinn-required">*</span></label>
                                        <input type="text" id="guest-last-name" name="last_name" class="bookinn-input" required>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="guest-email"><?php _e('Email Address', 'bookinn'); ?> <span class="bookinn-required">*</span></label>
                                        <input type="email" id="guest-email" name="email" class="bookinn-input" required>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="guest-phone"><?php _e('Phone Number', 'bookinn'); ?></label>
                                        <input type="tel" id="guest-phone" name="phone" class="bookinn-input">
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="guest-date-of-birth"><?php _e('Date of Birth', 'bookinn'); ?></label>
                                        <input type="date" id="guest-date-of-birth" name="date_of_birth" class="bookinn-input">
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="guest-nationality"><?php _e('Nationality', 'bookinn'); ?></label>
                                        <input type="text" id="guest-nationality" name="nationality" class="bookinn-input" placeholder="<?php _e('e.g., Italian, American', 'bookinn'); ?>">
                                    </div>
                                </div>
                            </div>

                            <!-- Address Information Section -->
                            <div class="bookinn-form-section bookinn-card">
                                <h5>
                                    <svg class="bookinn-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M9 22V12H15V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <?php _e('Address Information', 'bookinn'); ?>
                                </h5>
                                <div class="bookinn-form-group">
                                    <label for="guest-address"><?php _e('Address', 'bookinn'); ?></label>
                                    <textarea id="guest-address" name="address" class="bookinn-textarea" rows="3" placeholder="<?php _e('Street address, apartment, suite, etc.', 'bookinn'); ?>"></textarea>
                                </div>
                                <div class="bookinn-form-grid bookinn-form-grid-2">
                                    <div class="bookinn-form-group">
                                        <label for="guest-city"><?php _e('City', 'bookinn'); ?></label>
                                        <input type="text" id="guest-city" name="city" class="bookinn-input">
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="guest-postal-code"><?php _e('Postal Code', 'bookinn'); ?></label>
                                        <input type="text" id="guest-postal-code" name="postal_code" class="bookinn-input">
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="guest-country"><?php _e('Country', 'bookinn'); ?></label>
                                        <input type="text" id="guest-country" name="country" class="bookinn-input">
                                    </div>
                                </div>
                            </div>

                            <!-- Identification Section -->
                            <div class="bookinn-form-section bookinn-card">
                                <h5>
                                    <svg class="bookinn-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M12 14C13.6569 14 15 12.6569 15 11C15 9.34315 13.6569 8 12 8C10.3431 8 9 9.34315 9 11C9 12.6569 10.3431 14 12 14Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <?php _e('Identification', 'bookinn'); ?>
                                </h5>
                                <div class="bookinn-form-grid bookinn-form-grid-2">
                                    <div class="bookinn-form-group">
                                        <label for="guest-id-type"><?php _e('ID Type', 'bookinn'); ?></label>
                                        <select id="guest-id-type" name="id_type" class="bookinn-select">
                                            <option value=""><?php _e('Select ID Type', 'bookinn'); ?></option>
                                            <option value="passport"><?php _e('Passport', 'bookinn'); ?></option>
                                            <option value="id_card"><?php _e('ID Card', 'bookinn'); ?></option>
                                            <option value="driver_license"><?php _e('Driver License', 'bookinn'); ?></option>
                                            <option value="other"><?php _e('Other', 'bookinn'); ?></option>
                                        </select>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="guest-id-number"><?php _e('ID Number', 'bookinn'); ?></label>
                                        <input type="text" id="guest-id-number" name="id_number" class="bookinn-input">
                                    </div>
                                </div>
                            </div>

                            <!-- Notes Section -->
                            <div class="bookinn-form-section">
                                <h5><?php _e('Additional Notes', 'bookinn'); ?></h5>
                                <div class="bookinn-form-group">
                                    <label for="guest-notes"><?php _e('Notes', 'bookinn'); ?></label>
                                    <textarea id="guest-notes" name="notes" class="bookinn-textarea" rows="4" placeholder="<?php _e('Any additional information about the guest...', 'bookinn'); ?>"></textarea>
                                </div>
                            </div>

                            <input type="hidden" id="guest-id" name="id" value="">
                        </form>
                    </div>
                    <div class="bookinn-modal-footer">
                        <button class="bookinn-btn bookinn-btn-secondary bookinn-close-modal"><?php _e('Cancel', 'bookinn'); ?></button>
                        <button class="bookinn-btn bookinn-btn-primary" id="save-guest"><?php _e('Save Guest', 'bookinn'); ?></button>
                    </div>
                </div>
            </div>

            <!-- Enhanced Rate Plan Editor Modal -->
            <div class="bookinn-modal"
                 id="bookinn-rate-plan-modal"
                 role="dialog"
                 aria-labelledby="rate-plan-modal-title"
                 aria-hidden="true"
                 tabindex="-1"
                 style="display: none;">
                <div class="bookinn-modal-container bookinn-modal-wide" role="document">
                    <div class="bookinn-modal-header">
                           <img src="<?php echo BOOKINN_PLUGIN_URL . 'assets/images/logo_form.png'; ?>" alt="BookInn Logo" class="bookinn-modal-logo">
                        <h4 id="rate-plan-modal-title" tabindex="0"><?php _e('Rate Plan', 'bookinn'); ?></h4>
                        <button class="bookinn-close-modal"
                                id="close-rate-plan-modal"
                                aria-label="<?php _e('Close modal', 'bookinn'); ?>"
                                type="button">×</button>
                    </div>
                    <div class="bookinn-modal-body">
                        <form id="rate-plan-form" aria-labelledby="rate-plan-modal-title">

                            <!-- Basic Information Section -->
                            <div class="bookinn-form-section">
                                <h5><?php _e('Basic Information', 'bookinn'); ?></h5>
                                <div class="bookinn-form-grid bookinn-form-grid-2">
                                    <div class="bookinn-form-group">
                                        <label for="rate-plan-name"><?php _e('Rate Plan Name', 'bookinn'); ?> <span class="bookinn-required">*</span></label>
                                        <input type="text" id="rate-plan-name" name="name" class="bookinn-input" required>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="rate-plan-room-type"><?php _e('Room Type', 'bookinn'); ?> <span class="bookinn-required">*</span></label>
                                        <select id="rate-plan-room-type" name="room_type_id" class="bookinn-select" required>
                                            <option value=""><?php _e('Select Room Type', 'bookinn'); ?></option>
                                            <?php $this->render_room_type_options(); ?>
                                        </select>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="rate-plan-date"><?php _e('Rate Date', 'bookinn'); ?> <span class="bookinn-required">*</span></label>
                                        <input type="date" id="rate-plan-date" name="rate_date" class="bookinn-input" required>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="rate-plan-price"><?php _e('Rate Price (€)', 'bookinn'); ?> <span class="bookinn-required">*</span></label>
                                        <input type="number" id="rate-plan-price" name="rate_price" class="bookinn-input" step="0.01" min="0" required>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="rate-plan-base-price"><?php _e('Base Price (€)', 'bookinn'); ?></label>
                                        <input type="number" id="rate-plan-base-price" name="base_price" class="bookinn-input" step="0.01" min="0">
                                        <small class="bookinn-help-text">
                                            <?php _e('Base price is used for internal calculations and cost comparisons. The Rate Price above is what customers pay.', 'bookinn'); ?>
                                        </small>
                                        <!-- Provenienza del dato e pulsante copia -->
                                        <div style="margin-top:8px; display:flex; align-items:center; gap:8px;">
                                            <small id="rate-plan-base-source" class="bookinn-help-text" style="margin:0;">
                                                <?php _e('Value pre-filled from the base price of the selected room type (if available).', 'bookinn'); ?>
                                            </small>
                                            <button type="button" class="bookinn-btn bookinn-btn-sm bookinn-btn-secondary" id="copy-base-to-rate">
                                                <?php _e('Fill Rate price with Base price', 'bookinn'); ?>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="rate-plan-is-active"><?php _e('Active Status', 'bookinn'); ?></label>
                                        <label class="bookinn-toggle-label">
                                            <input type="checkbox" id="rate-plan-is-active" name="is_active" value="1" checked>
                                            <span class="bookinn-toggle-slider"></span>
                                            <span class="bookinn-toggle-text"><?php _e('Active', 'bookinn'); ?></span>
                                        </label>
                                        <small class="bookinn-help-text"><?php _e('Enable/disable this rate plan', 'bookinn'); ?></small>
                                    </div>
                                </div>
                                <div class="bookinn-form-group">
                                    <label for="rate-plan-short-description"><?php _e('Short Description', 'bookinn'); ?></label>
                                    <textarea id="rate-plan-short-description" name="short_description" class="bookinn-textarea" rows="2" placeholder="<?php _e('Brief description for display...', 'bookinn'); ?>"></textarea>
                                </div>
                            </div>

                            <!-- Policies Section -->
                            <div class="bookinn-form-section">
                                <h5><?php _e('Policies', 'bookinn'); ?></h5>
                                <div class="bookinn-form-grid bookinn-form-grid-2">
                                    <div class="bookinn-form-group">
                                        <label for="rate-plan-cancellation-policy"><?php _e('Cancellation Policy', 'bookinn'); ?></label>
                                        <select id="rate-plan-cancellation-policy" name="cancellation_policy" class="bookinn-select">
                                            <option value="flexible"><?php _e('Flexible', 'bookinn'); ?></option>
                                            <option value="semi_flexible"><?php _e('Semi-Flexible', 'bookinn'); ?></option>
                                            <option value="non_refundable"><?php _e('Non-Refundable', 'bookinn'); ?></option>
                                            <option value="custom"><?php _e('Custom', 'bookinn'); ?></option>
                                        </select>
                                        
                                        <!-- Cancellation Policy Help Text -->
                                        <div class="bookinn-cancellation-help">
                                            <h6><?php _e('Cancellation Policy Guidelines', 'bookinn'); ?></h6>
                                            <ul>
                                                <li><strong><?php _e('Flexible:', 'bookinn'); ?></strong> <?php _e('Free cancellation up to 24 hours before check-in', 'bookinn'); ?></li>
                                                <li><strong><?php _e('Semi-Flexible:', 'bookinn'); ?></strong> <?php _e('Free cancellation up to 7 days before check-in, 50% charge thereafter', 'bookinn'); ?></li>
                                                <li><strong><?php _e('Non-Refundable:', 'bookinn'); ?></strong> <?php _e('No cancellation allowed - full payment required', 'bookinn'); ?></li>
                                                <li><strong><?php _e('Custom:', 'bookinn'); ?></strong> <?php _e('Specify custom cancellation terms below', 'bookinn'); ?></li>
                                            </ul>
                                        </div>
                                        
                                        <!-- Custom Cancellation Field -->
                                        <div class="bookinn-custom-cancellation-field" id="custom-cancellation-field">
                                            <label for="rate-plan-custom-cancellation-days">
                                                <?php _e('Custom Cancellation Days', 'bookinn'); ?> <span class="bookinn-required">*</span>
                                            </label>
                                            <input 
                                                type="number" 
                                                id="rate-plan-custom-cancellation-days" 
                                                name="custom_cancellation_days" 
                                                class="bookinn-input" 
                                                min="0" 
                                                max="365"
                                                placeholder="<?php _e('Number of days', 'bookinn'); ?>"
                                            >
                                            <small class="bookinn-help-text">
                                                <?php _e('Enter the number of days before check-in when cancellations can be made without penalties. 0 means no cancellation allowed.', 'bookinn'); ?>
                                            </small>
                                        </div>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="rate-plan-days-cancellation"><?php _e('Days for Cancellation', 'bookinn'); ?></label>
                                        <input type="number" id="rate-plan-days-cancellation" name="days_cancellation" class="bookinn-input" min="0" max="365">
                                        <small class="bookinn-help-text"><?php _e('Number of days before check-in when cancellation is allowed (applies to all policies)', 'bookinn'); ?></small>
                                    </div>
                                </div>
                            </div>

                            <!-- Meal Plan Section -->
                            <div class="bookinn-form-section">
                                <h5><?php _e('Meal Plan', 'bookinn'); ?></h5>
                                <div class="bookinn-form-grid bookinn-form-grid-2">
                                    <div class="bookinn-form-group">
                                        <label for="rate-plan-meal-plan"><?php _e('Board Type', 'bookinn'); ?></label>
                                        <select id="rate-plan-meal-plan" name="meal_plan" class="bookinn-select">
                                            <option value="room_only"><?php _e('Room Only', 'bookinn'); ?></option>
                                            <option value="bed_breakfast"><?php _e('Bed & Breakfast', 'bookinn'); ?></option>
                                            <option value="half_board"><?php _e('Half Board', 'bookinn'); ?></option>
                                            <option value="full_board"><?php _e('Full Board', 'bookinn'); ?></option>
                                            <option value="all_inclusive"><?php _e('All Inclusive', 'bookinn'); ?></option>
                                        </select>
                                        <small class="bookinn-help-text">
                                            <?php _e('Select the meal plan included with this rate', 'bookinn'); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Stay Restrictions Section -->
                            <div class="bookinn-form-section">
                                <h5><?php _e('Stay Restrictions', 'bookinn'); ?></h5>
                                <div class="bookinn-form-grid bookinn-form-grid-2">
                                    <div class="bookinn-form-group">
                                        <label for="rate-plan-min-stay"><?php _e('Minimum Stay (nights)', 'bookinn'); ?></label>
                                        <input type="number" id="rate-plan-min-stay" name="min_stay" class="bookinn-input" min="1" value="1">
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="rate-plan-max-stay"><?php _e('Maximum Stay (nights)', 'bookinn'); ?></label>
                                        <input type="number" id="rate-plan-max-stay" name="max_stay" class="bookinn-input" min="1">
                                    </div>
                                </div>
                            </div>

                            <!-- Inclusions Section -->
                            <div class="bookinn-form-section">
                                <h5><?php _e('Additional Inclusions', 'bookinn'); ?></h5>
                                <div class="bookinn-form-grid bookinn-form-grid-2">
                                    <div class="bookinn-form-group">
                                        <label class="bookinn-checkbox-label">
                                            <input type="checkbox" id="rate-plan-includes-wifi" name="includes_wifi" value="1">
                                            <span class="bookinn-checkbox-custom"></span>
                                            <?php _e('WiFi Included', 'bookinn'); ?>
                                        </label>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label class="bookinn-checkbox-label">
                                            <input type="checkbox" id="rate-plan-includes-spa" name="includes_spa" value="1">
                                            <span class="bookinn-checkbox-custom"></span>
                                            <?php _e('Spa Access', 'bookinn'); ?>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Pricing Modifiers Section -->
                            <div class="bookinn-form-section">
                                <h5><?php _e('Pricing Modifiers', 'bookinn'); ?></h5>
                                <div class="bookinn-form-grid bookinn-form-grid-2">
                                    <div class="bookinn-form-group">
                                        <label for="rate-plan-weekend-surcharge"><?php _e('Weekend Surcharge (%)', 'bookinn'); ?></label>
                                        <input type="number" id="rate-plan-weekend-surcharge" name="weekend_surcharge" class="bookinn-input" step="0.01" min="0" max="100">
                                        <small class="bookinn-help-text"><?php _e('Additional percentage for weekends', 'bookinn'); ?></small>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="rate-plan-holiday-surcharge"><?php _e('Holiday Surcharge (%)', 'bookinn'); ?></label>
                                        <input type="number" id="rate-plan-holiday-surcharge" name="holiday_surcharge" class="bookinn-input" step="0.01" min="0" max="100">
                                        <small class="bookinn-help-text"><?php _e('Additional percentage for holidays', 'bookinn'); ?></small>
                                    </div>
                                </div>
                                <div class="bookinn-form-grid bookinn-form-grid-2">
                                    <div class="bookinn-form-group">
                                        <label class="bookinn-checkbox-label">
                                            <input type="checkbox" id="rate-plan-weekend" name="is_weekend" value="1">
                                            <span class="bookinn-checkbox-custom"></span>
                                            <?php _e('Weekend Rate', 'bookinn'); ?>
                                        </label>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label class="bookinn-checkbox-label">
                                            <input type="checkbox" id="rate-plan-holiday" name="is_holiday" value="1">
                                            <span class="bookinn-checkbox-custom"></span>
                                            <?php _e('Holiday Rate', 'bookinn'); ?>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Availability Section -->
                            <div class="bookinn-form-section">
                                <h5><?php _e('Availability Period', 'bookinn'); ?></h5>
                                <div class="bookinn-form-grid bookinn-form-grid-2">
                                    <div class="bookinn-form-group">
                                        <label for="rate-plan-availability-start"><?php _e('Start Date', 'bookinn'); ?></label>
                                        <input type="date" id="rate-plan-availability-start" name="availability_start_date" class="bookinn-input">
                                        <small class="bookinn-help-text"><?php _e('When this rate plan becomes available', 'bookinn'); ?></small>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="rate-plan-availability-end"><?php _e('End Date', 'bookinn'); ?></label>
                                        <input type="date" id="rate-plan-availability-end" name="availability_end_date" class="bookinn-input">
                                        <small class="bookinn-help-text"><?php _e('When this rate plan expires', 'bookinn'); ?></small>
                                    </div>
                                </div>
                            </div>

                            <!-- Distribution Channels Section -->
                            <div class="bookinn-form-section">
                                <h5><?php _e('Distribution Channels', 'bookinn'); ?></h5>
                                <div class="bookinn-form-grid bookinn-form-grid-2">
                                    <div class="bookinn-form-group">
                                        <label class="bookinn-checkbox-label">
                                            <input type="checkbox" id="rate-plan-channel-official" name="channel_official_site" value="1" checked>
                                            <span class="bookinn-checkbox-custom"></span>
                                            <?php _e('Official Website', 'bookinn'); ?>
                                        </label>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label class="bookinn-checkbox-label">
                                            <input type="checkbox" id="rate-plan-channel-ota" name="channel_ota" value="1">
                                            <span class="bookinn-checkbox-custom"></span>
                                            <?php _e('OTA Platforms', 'bookinn'); ?>
                                        </label>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label class="bookinn-checkbox-label">
                                            <input type="checkbox" id="rate-plan-channel-corporate" name="channel_corporate" value="1">
                                            <span class="bookinn-checkbox-custom"></span>
                                            <?php _e('Corporate Bookings', 'bookinn'); ?>
                                        </label>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <!-- Additional field: Guest Type Restriction -->
                                        <label for="rate-plan-guest-type"><?php _e('Guest Type Restriction', 'bookinn'); ?></label>
                                        <select id="rate-plan-guest-type" name="guest_type_restriction" class="bookinn-select">
                                            <option value=""><?php _e('No Restriction', 'bookinn'); ?></option>
                                            <option value="adults_only"><?php _e('Adults Only', 'bookinn'); ?></option>
                                            <option value="families_welcome"><?php _e('Families Welcome', 'bookinn'); ?></option>
                                            <option value="business_travelers"><?php _e('Business Travelers', 'bookinn'); ?></option>
                                            <option value="groups_only"><?php _e('Groups Only', 'bookinn'); ?></option>
                                        </select>
                                        <small class="bookinn-help-text"><?php _e('Restrict this rate plan to specific guest types', 'bookinn'); ?></small>
                                    </div>
                                </div>
                            </div>

                            <!-- Description Section -->
                            <div class="bookinn-form-section">
                                <h5><?php _e('Description', 'bookinn'); ?></h5>
                                <div class="bookinn-form-group">
                                    <label for="rate-plan-description"><?php _e('Detailed Description', 'bookinn'); ?></label>
                                    <textarea id="rate-plan-description" name="description" class="bookinn-textarea" rows="4" placeholder="<?php _e('Detailed description for this rate plan...', 'bookinn'); ?>"></textarea>
                                </div>
                            </div>

                            <input type="hidden" id="rate-plan-id" name="id" value="">
                        </form>
                    </div>
                    <div class="bookinn-modal-footer">
                        <button class="bookinn-btn bookinn-btn-secondary bookinn-close-modal"><?php _e('Cancel', 'bookinn'); ?></button>
                        <button class="bookinn-btn bookinn-btn-primary" id="save-rate-plan"><?php _e('Save Rate Plan', 'bookinn'); ?></button>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render rooms table rows
     */
    private function render_rooms_table_rows() {
        $rooms = BookInn_Database_Manager::get_results("
            SELECT r.*, rt.name as room_type_name, rt.base_price, rt.max_guests
            FROM {rooms} r
            LEFT JOIN {room_types} rt ON r.room_type_id = rt.id
            WHERE r.is_active = 1
            ORDER BY r.room_number
        ");

        if (empty($rooms)) {
            echo '<tr><td colspan="6" class="bookinn-no-data">' . __('No rooms found. Add your first room to get started.', 'bookinn') . '</td></tr>';
            return;
        }

        foreach ($rooms as $room) {
            $status_class = 'bookinn-status-' . $room->status;
            $status_text = ucfirst(str_replace('_', ' ', $room->status));
            ?>
            <tr data-room-id="<?php echo esc_attr($room->id); ?>" data-room-number="<?php echo esc_attr($room->room_number); ?>" data-room-type="<?php echo esc_attr($room->room_type_name); ?>" data-base-price="<?php echo esc_attr($room->base_price); ?>">
                <td>
                    <strong><?php echo esc_html($room->room_number); ?></strong>
                    <?php if ($room->name): ?>
                        <br><small><?php echo esc_html($room->name); ?></small>
                    <?php endif; ?>
                </td>
                <td><?php echo esc_html($room->room_type_name); ?></td>
                <td><?php echo esc_html($room->floor ?: '-'); ?></td>
                <td>
                    <span class="bookinn-status-badge <?php echo esc_attr($status_class); ?>">
                        <?php echo esc_html($status_text); ?>
                    </span>
                </td>
                <td>€<?php echo esc_html(number_format($room->base_price, 2)); ?></td>
                <td>
                    <div class="bookinn-actions">
                        <button class="bookinn-btn bookinn-btn-sm bookinn-btn-secondary bookinn-edit-room" data-room-id="<?php echo esc_attr($room->id); ?>">
                            <?php _e('Edit', 'bookinn'); ?>
                        </button>
                        <button class="bookinn-btn bookinn-btn-sm bookinn-btn-secondary bookinn-view-room-calendar" data-room-id="<?php echo esc_attr($room->id); ?>">
                            <?php _e('Calendar', 'bookinn'); ?>
                        </button>
                        <button class="bookinn-btn bookinn-btn-sm bookinn-btn-success bookinn-quick-book-room" 
                                data-room-id="<?php echo esc_attr($room->id); ?>" 
                                data-room-number="<?php echo esc_attr($room->room_number); ?>"
                                data-room-type="<?php echo esc_attr($room->room_type_name); ?>">
                            <?php _e('Quick Book', 'bookinn'); ?>
                        </button>
                        <button class="bookinn-btn bookinn-btn-sm bookinn-btn-danger bookinn-delete-room" data-room-id="<?php echo esc_attr($room->id); ?>">
                            <?php _e('Delete', 'bookinn'); ?>
                        </button>
                    </div>
                </td>
            </tr>
            <?php
        }
    }

    /**
     * Render room types table rows
     */
    private function render_room_types_table_rows() {
        $room_types = BookInn_Database_Manager::get_results("
            SELECT rt.*, COUNT(r.id) as room_count
            FROM {room_types} rt
            LEFT JOIN {rooms} r ON r.room_type_id = rt.id AND r.is_active = 1
            WHERE rt.is_active = 1
            GROUP BY rt.id
            ORDER BY rt.name
        ");

        if (empty($room_types)) {
            echo '<tr><td colspan="5" class="bookinn-no-data">' . __('No room types found. Add your first room type to get started.', 'bookinn') . '</td></tr>';
            return;
        }

        foreach ($room_types as $type) {
            ?>
            <tr data-room-type-id="<?php echo esc_attr($type->id); ?>">
                <td>
                    <strong><?php echo esc_html($type->name); ?></strong>
                    <br><small><?php printf(__('%d rooms', 'bookinn'), $type->room_count); ?></small>
                </td>
                <td><?php echo esc_html($type->description ?: '-'); ?></td>
                <td>€<?php echo esc_html(number_format($type->base_price, 2)); ?></td>
                <td><?php echo esc_html($type->max_guests); ?></td>
                <td>
                    <div class="bookinn-actions">
                        <button class="bookinn-btn bookinn-btn-sm bookinn-btn-secondary bookinn-edit-room-type" data-room-type-id="<?php echo esc_attr($type->id); ?>">
                            <?php _e('Edit', 'bookinn'); ?>
                        </button>
                        <?php if ($type->room_count == 0): ?>
                        <button class="bookinn-btn bookinn-btn-sm bookinn-btn-danger bookinn-delete-room-type" data-room-type-id="<?php echo esc_attr($type->id); ?>">
                            <?php _e('Delete', 'bookinn'); ?>
                        </button>
                        <?php else: ?>
                        <span class="bookinn-text-muted" title="<?php _e('Cannot delete room type with assigned rooms', 'bookinn'); ?>">
                            <?php printf(__('(%d rooms)', 'bookinn'), $type->room_count); ?>
                        </span>
                        <?php endif; ?>
                    </div>
                </td>
            </tr>
            <?php
        }
    }

    /**
     * Render rooms grid
     */
    private function render_rooms_grid() {
        $rooms = BookInn_Database_Manager::get_results("
            SELECT r.*, rt.name as room_type_name, rt.base_price, rt.max_guests
            FROM {rooms} r
            LEFT JOIN {room_types} rt ON r.room_type_id = rt.id
            WHERE r.is_active = 1
            ORDER BY r.room_number
        ");

        if (empty($rooms)) {
            echo '<div class="bookinn-no-data">' . __('No rooms found. Add your first room to get started.', 'bookinn') . '</div>';
            return;
        }

        foreach ($rooms as $room) {
            $status_class = 'bookinn-room-' . $room->status;
            $status_text = ucfirst(str_replace('_', ' ', $room->status));
            ?>
            <div class="bookinn-room-card <?php echo esc_attr($status_class); ?>" data-room-id="<?php echo esc_attr($room->id); ?>">
                <div class="bookinn-room-header">
                    <h4 class="bookinn-room-number"><?php echo esc_html($room->room_number); ?></h4>
                    <span class="bookinn-room-status"><?php echo esc_html($status_text); ?></span>
                </div>

                <div class="bookinn-room-info">
                    <p class="bookinn-room-type"><?php echo esc_html($room->room_type_name); ?></p>
                    <?php if ($room->name): ?>
                    <p class="bookinn-room-name"><?php echo esc_html($room->name); ?></p>
                    <?php endif; ?>
                    <p class="bookinn-room-details">
                        <?php 
                        $floor = isset($room->floor) && $room->floor ? $room->floor : 0;
                        $max_guests = isset($room->max_guests) && $room->max_guests ? $room->max_guests : 2;
                        printf(__('Floor %d • Max %d guests', 'bookinn'), $floor, $max_guests); 
                        ?>
                    </p>
                    <p class="bookinn-room-price">€<?php echo esc_html(number_format($room->base_price, 2)); ?>/night</p>
                </div>

                <div class="bookinn-room-actions">
                    <button class="bookinn-btn-icon bookinn-edit-room" data-room-id="<?php echo esc_attr($room->id); ?>" title="<?php _e('Edit Room', 'bookinn'); ?>">
                        <i class="fa-solid fa-pen-to-square bookinn-fa"></i>
                    </button>
                    <button class="bookinn-btn-icon bookinn-room-calendar" data-room-id="<?php echo esc_attr($room->id); ?>" title="<?php _e('View Calendar', 'bookinn'); ?>">
                        <i class="fa-solid fa-calendar-days bookinn-fa"></i>
                    </button>
                    <?php if ($room->status === 'available'): ?>
                    <button class="bookinn-btn-icon bookinn-quick-book" data-room-id="<?php echo esc_attr($room->id); ?>" title="<?php _e('Quick Book', 'bookinn'); ?>">
                        <i class="fa-solid fa-plus bookinn-fa"></i>
                    </button>
                    <?php endif; ?>
                </div>
            </div>
            <?php
        }
    }

    /**
     * Render room type options
     */
    private function render_room_type_options() {
        $room_types = BookInn_Database_Manager::get_results("
            SELECT id, name as room_type, base_price
            FROM {room_types}
            WHERE is_active = 1
            ORDER BY name
        ");

        foreach ($room_types as $type) {
            // Include data-base-price attribute so frontend can auto-fill base price in rate plan modal
            printf(
                '<option value="%s" data-base-price="%s">%s (€%s/night)</option>',
                esc_attr($type->id),
                esc_attr(number_format($type->base_price, 2)),
                esc_html(ucfirst($type->room_type)),
                esc_html(number_format($type->base_price, 2))
            );
        }
    }

    /**
     * Render rate plans table rows
     */
    private function render_rate_plans_table_rows() {
        $rate_plans = BookInn_Database_Manager::get_results("
            SELECT rp.*, rt.name as room_type_name
            FROM {rate_plans} rp
            LEFT JOIN {room_types} rt ON rp.room_type_id = rt.id          
            ORDER BY rp.rate_date DESC, rt.name ASC
        ");

        if (empty($rate_plans)) {
            echo '<tr><td colspan="11" class="bookinn-no-data">' . __('No rate plans found. Add your first rate plan to get started.', 'bookinn') . '</td></tr>';
            return;
        }

        foreach ($rate_plans as $plan) {
            $weekend_badge = $plan->is_weekend ? '<span class="bookinn-badge bookinn-badge-info">' . __('Weekend', 'bookinn') . '</span>' : '';
            $holiday_badge = $plan->is_holiday ? '<span class="bookinn-badge bookinn-badge-warning">' . __('Holiday', 'bookinn') . '</span>' : '';
            $status_class = $plan->is_active ? 'bookinn-status-active' : 'bookinn-status-inactive';
            $status_text = $plan->is_active ? __('Active', 'bookinn') : __('Inactive', 'bookinn');

            ?>
            <tr data-rate-plan-id="<?php echo esc_attr($plan->id); ?>">
                <td>
                    <input type="checkbox" class="rate-plan-checkbox" value="<?php echo esc_attr($plan->id); ?>">
                </td>
                <td>
                    <strong><?php echo esc_html($plan->name); ?></strong>
                    <?php if ($plan->description): ?>
                    <br><small class="bookinn-text-muted"><?php echo esc_html($plan->description); ?></small>
                    <?php endif; ?>
                </td>
                <td><?php echo esc_html($plan->room_type_name); ?></td>
                <td><?php echo esc_html(date_i18n(get_option('date_format'), strtotime($plan->rate_date))); ?></td>
                <td>
                    <strong>€<?php echo esc_html(number_format($plan->rate_price, 2)); ?></strong>
                </td>
                <td><?php echo esc_html($plan->min_stay ?: '1'); ?></td>
                <td><?php echo esc_html($plan->max_stay ?: '-'); ?></td>
                <td><?php echo $weekend_badge; ?></td>
                <td><?php echo $holiday_badge; ?></td>
                <td>
                    <span class="bookinn-status <?php echo esc_attr($status_class); ?>">
                        <?php echo esc_html($status_text); ?>
                    </span>
                </td>
                <td>
                    <div class="bookinn-rate-plan-actions">
                        <button class="bookinn-btn bookinn-btn-sm bookinn-btn-secondary bookinn-edit-rate-plan" data-rate-plan-id="<?php echo esc_attr($plan->id); ?>">
                            <?php _e('Edit', 'bookinn'); ?>
                        </button>
                        <button class="bookinn-btn bookinn-btn-sm bookinn-btn-danger bookinn-delete-rate-plan" data-rate-plan-id="<?php echo esc_attr($plan->id); ?>">
                            <?php _e('Delete', 'bookinn'); ?>
                        </button>
                    </div>
                </td>
            </tr>
            <?php
        }
    }

    /**
     * Render Calendar Tab
     */
    private function render_calendar_tab() {
        // Check if our integration class exists
        if (!class_exists('BookInn_Occupancy_Calendar_Integration')) {
            require_once BOOKINN_PLUGIN_PATH . 'includes/class-bookinn-occupancy-calendar-integration.php';
        }
        
        // Get the singleton instance and render the calendar
        $calendar = BookInn_Occupancy_Calendar_Integration::get_instance();
        
        ?>
        <!-- HEADER: Section Title & Description -->
         <div class="bookinn-occupancy-calendar-container">
        <div class="bookinn-section-header" style="display: flex; align-items: flex-start; justify-content: space-between; gap: 12px;">
            <div style="flex:1;">
                <h3 style="margin-bottom: 0;"><?php _e('Occupancy Calendar', 'bookinn'); ?></h3>
                <p class="bookinn-section-description" style="margin-top: 4px; margin-bottom: 0; color: #6b7280; font-size: 13px; font-weight: 400;">
                    <?php _e('Interactive calendar view showing room occupancy', 'bookinn'); ?>
                </p>
            </div>
            <!-- Fullscreen button: reuse Gantt SVG icon, placed top-right -->
            <div style="margin-left:12px;display:flex;align-items:flex-start;">
                <button class="bookinn-occupancy-fullscreen-btn" title="<?php _e('Fullscreen', 'bookinn'); ?>" onclick="bookinnToggleOccupancyFullscreen(event)" style="background: none; border: none; cursor: pointer; padding: 6px; margin-left: 8px;">
                    <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect x="3" y="3" width="6" height="2" rx="1" fill="#374151"/>
                        <rect x="3" y="3" width="2" height="6" rx="1" fill="#374151"/>
                        <rect x="13" y="3" width="6" height="2" rx="1" fill="#374151"/>
                        <rect x="17" y="3" width="2" height="6" rx="1" fill="#374151"/>
                        <rect x="3" y="17" width="6" height="2" rx="1" fill="#374151"/>
                        <rect x="3" y="13" width="2" height="6" rx="1" fill="#374151"/>
                        <rect x="13" y="17" width="6" height="2" rx="1" fill="#374151"/>
                        <rect x="17" y="13" width="2" height="6" rx="1" fill="#374151"/>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Calendar Content -->
         
         
        <div class="bookinn-calendar-content">
            <?php echo $calendar->render_dashboard_calendar(); ?>
        </div>
        </div>
        <?php
    }
       
    /**
     * Render room options for filters
     */
    private function render_room_options() {
        $rooms = BookInn_Database_Manager::get_results("
            SELECT r.id, r.room_number, r.name, rt.name as room_type
            FROM {rooms} r
            LEFT JOIN {room_types} rt ON r.room_type_id = rt.id
            WHERE r.is_active = 1
            ORDER BY r.room_number
        ");

        foreach ($rooms as $room) {
            $display_name = $room->room_number;
            if ($room->name) {
                $display_name .= ' - ' . $room->name;
            }
            if ($room->room_type) {
                $display_name .= ' (' . ucfirst($room->room_type) . ')';
            }

            printf(
                '<option value="%d">%s</option>',
                esc_attr($room->id),
                esc_html($display_name)
            );
        }
    }    
    
    /**
     * Render Reports Tab
     */
    private function render_reports_tab() {
        // Get reports metrics
        $metrics = $this->get_reports_metrics();
        ?>
        <!-- Reports Container -->
        <div class="bookinn-reports-container">
            <!-- Reports Header -->
            <div class="bookinn-section-header">
                <h3><?php _e('Analytics & Reports', 'bookinn'); ?></h3>
                <div class="bookinn-section-actions">
                    <select id="report-period" class="bookinn-select">
                        <option value="7"><?php _e('Last 7 days', 'bookinn'); ?></option>
                        <option value="30" selected><?php _e('Last 30 days', 'bookinn'); ?></option>
                        <option value="90"><?php _e('Last 3 months', 'bookinn'); ?></option>
                        <option value="365"><?php _e('Last year', 'bookinn'); ?></option>
                    </select>
                    <button class="bookinn-btn bookinn-btn-primary" id="export-reports">
                        <i class="fa-solid fa-download bookinn-fa"></i>
                        <?php _e('Export', 'bookinn'); ?>
                    </button>
                </div>
            </div>

            <!-- Key Performance Indicators -->
            <div class="bookinn-metrics-grid">
                <div class="bookinn-metric-card">
                    <div class="bookinn-metric-icon">
                        <i class="fa-solid fa-euro-sign bookinn-fa"></i>
                </div>
                        <div class="bookinn-metric-content">
                        <h3 class="bookinn-metric-value">€<?php echo esc_html(number_format($metrics['total_revenue'], 2)); ?></h3>
                        <p class="bookinn-metric-label"><?php _e('Revenue Performance', 'bookinn'); ?></p>
                        <span class="bookinn-metric-change <?php echo $metrics['revenue_change'] >= 0 ? 'positive' : 'negative'; ?>">
                                <?php echo $metrics['revenue_change'] >= 0 ? '+' : ''; ?><?php echo esc_html($metrics['revenue_change']); ?>%</span>
                        </div>
                </div>

                <div class="bookinn-metric-card">
                    <div class="bookinn-metric-icon">
                        <i class="fa-solid fa-percent bookinn-fa"></i>
                    </div>
                    <div class="bookinn-metric-content">
                        <h3 class="bookinn-metric-value"><?php echo esc_html($metrics['occupancy_rate']); ?>%</h3>
                        <p class="bookinn-metric-label"><?php _e('Occupancy Rate', 'bookinn'); ?></p>
                        <span class="bookinn-metric-change <?php echo $metrics['occupancy_change'] >= 0 ? 'positive' : 'negative'; ?>">
                            <?php echo $metrics['occupancy_change'] >= 0 ? '+' : ''; ?><?php echo esc_html($metrics['occupancy_change']); ?>%
                        </span>
                    </div>
                </div>

                <div class="bookinn-metric-card">
                    <div class="bookinn-metric-icon">
                        <i class="fa-solid fa-calendar-check bookinn-fa"></i>
                    </div>
                    <div class="bookinn-metric-content">
                    <h3 class="bookinn-metric-value">€<?php echo esc_html(number_format($metrics['avg_daily_rate'], 2)); ?></h3>
                    <p class="bookinn-metric-label"><?php _e('Average Daily Rate', 'bookinn'); ?></p>  
                    <span class="bookinn-metric-change <?php echo $metrics['adr_change'] >= 0 ? 'positive' : 'negative'; ?>">
                        <?php echo $metrics['adr_change'] >= 0 ? '+' : ''; ?><?php echo esc_html($metrics['adr_change']); ?>%</span>
                    </div>
                </div>

                <div class="bookinn-metric-card">
                    <div class="bookinn-metric-icon">
                        <i class="fa-solid fa-calendar-check bookinn-fa"></i>
                    </div>
                    <div class="bookinn-metric-content">
                    <h3 class="bookinn-metric-value">€<?php echo esc_html(number_format($metrics['revpar'], 2)); ?></h3>
                    <p class="bookinn-metric-label"><?php _e('RevPAR', 'bookinn'); ?></p> 
                    <span class="bookinn-metric-change <?php echo $metrics['revpar_change'] >= 0 ? 'positive' : 'negative'; ?>">
                        <?php echo $metrics['revpar_change'] >= 0 ? '+' : ''; ?><?php echo esc_html($metrics['revpar_change']); ?>%</span>
                   </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="bookinn-reports-charts bookinn-charts-grid">
                <div class="bookinn-chart-container">
                    <div class="bookinn-chart-header">
                        <h4><?php _e('Revenue by Month', 'bookinn'); ?></h4>
                    </div>
                    <div class="bookinn-chart-wrapper">
                        <canvas id="revenue-by-month-chart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="bookinn-chart-container">
                    <div class="bookinn-chart-header">
                        <h4><?php _e('Occupancy Trends', 'bookinn'); ?></h4>
                    </div>
                    <div class="bookinn-chart-wrapper">
                        <canvas id="occupancy-trends-chart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="bookinn-chart-container">
                    <div class="bookinn-chart-header">
                        <h4><?php _e('Room Type Performance', 'bookinn'); ?></h4>
                    </div>
                    <div class="bookinn-chart-wrapper">
                        <canvas id="room-type-performance-chart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="bookinn-chart-container">
                    <div class="bookinn-chart-header">
                        <h4><?php _e('Booking Sources', 'bookinn'); ?></h4>
                    </div>
                    <div class="bookinn-chart-wrapper">
                        <canvas id="booking-sources-chart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Additional Reports Table -->
            <div class="bookinn-reports-table">
                <div class="bookinn-section-header">
                    <h4><?php _e('Detailed Analytics', 'bookinn'); ?></h4>
                </div>
                <div class="bookinn-table-wrapper">
                    <table class="bookinn-table">
                        <thead>
                            <tr>
                                <th><?php _e('Metric', 'bookinn'); ?></th>
                                <th><?php _e('Current Period', 'bookinn'); ?></th>
                                <th><?php _e('Previous Period', 'bookinn'); ?></th>
                                <th><?php _e('Change', 'bookinn'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><?php _e('Total Bookings', 'bookinn'); ?></td>
                                <td><?php echo esc_html($metrics['total_bookings']); ?></td>
                                <td><?php echo esc_html($metrics['prev_bookings']); ?></td>
                                <td><span class="<?php echo $metrics['bookings_change'] >= 0 ? 'positive' : 'negative'; ?>"><?php echo $metrics['bookings_change'] >= 0 ? '+' : ''; ?><?php echo esc_html($metrics['bookings_change']); ?>%</span></td>
                            </tr>
                            <tr>
                                <td><?php _e('Total Revenue', 'bookinn'); ?></td>
                                <td>€<?php echo esc_html(number_format($metrics['total_revenue'], 2)); ?></td>
                                <td>€<?php echo esc_html(number_format($metrics['prev_revenue'], 2)); ?></td>
                                <td><span class="<?php echo $metrics['revenue_change'] >= 0 ? 'positive' : 'negative'; ?>"><?php echo $metrics['revenue_change'] >= 0 ? '+' : ''; ?><?php echo esc_html($metrics['revenue_change']); ?>%</span></td>
                            </tr>
                            <tr>
                                <td><?php _e('Average Stay Length', 'bookinn'); ?></td>
                                <td><?php echo esc_html($metrics['avg_stay_length']); ?> <?php _e('days', 'bookinn'); ?></td>
                                <td><?php echo esc_html($metrics['prev_avg_stay']); ?> <?php _e('days', 'bookinn'); ?></td>
                                <td><span class="<?php echo $metrics['stay_change'] >= 0 ? 'positive' : 'negative'; ?>"><?php echo $metrics['stay_change'] >= 0 ? '+' : ''; ?><?php echo esc_html($metrics['stay_change']); ?>%</span></td>
                            </tr>
                            <tr>
                                <td><?php _e('Cancellation Rate', 'bookinn'); ?></td>
                                <td><?php echo esc_html($metrics['cancellation_rate']); ?>%</td>
                                <td><?php echo esc_html($metrics['prev_cancellation_rate']); ?>%</td>
                                <td><span class="<?php echo $metrics['cancellation_change'] <= 0 ? 'positive' : 'negative'; ?>"><?php echo $metrics['cancellation_change'] >= 0 ? '+' : ''; ?><?php echo esc_html($metrics['cancellation_change']); ?>%</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render Sidebar
     */
    private function render_sidebar() {
        ?>
        <div class="bookinn-sidebar-content">
            <!-- Quick Actions Widget -->
            <div class="bookinn-sidebar-widget">
                <div class="bookinn-widget-header">
                    <h4><?php _e('Quick Actions', 'bookinn'); ?></h4>
                </div>
                <div class="bookinn-widget-content">
                    <div class="bookinn-quick-actions">
                        <button class="bookinn-quick-action" id="quick-new-booking">
                            <i class="fa-solid fa-plus bookinn-fa"></i>
                            <span><?php _e('New Booking', 'bookinn'); ?></span>
                        </button>
                        <button class="bookinn-quick-action" id="quick-check-in">
                            <i class="fa-solid fa-right-to-bracket bookinn-fa"></i>
                            <span><?php _e('Check In', 'bookinn'); ?></span>
                        </button>
                        <button class="bookinn-quick-action" id="quick-check-out">
                            <i class="fa-solid fa-right-from-bracket bookinn-fa"></i>
                            <span><?php _e('Check Out', 'bookinn'); ?></span>
                        </button>
                        <button class="bookinn-quick-action" id="quick-room-status">
                            <i class="fa-solid fa-gear bookinn-fa"></i>
                            <span><?php _e('Room Status', 'bookinn'); ?></span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Today's Arrivals -->
            <div class="bookinn-sidebar-widget">
                <div class="bookinn-widget-header">
                    <h4><?php _e('Today\'s Arrivals', 'bookinn'); ?></h4>
                    <span class="bookinn-widget-count" id="arrivals-count">0</span>
                </div>
                <div class="bookinn-widget-content">
                    <div class="bookinn-arrivals-list" id="todays-arrivals">
                        <?php $this->render_todays_arrivals(); ?>
                    </div>
                </div>
            </div>

            <!-- Today's Departures -->
            <div class="bookinn-sidebar-widget">
                <div class="bookinn-widget-header">
                    <h4><?php _e('Today\'s Departures', 'bookinn'); ?></h4>
                    <span class="bookinn-widget-count" id="departures-count">0</span>
                </div>
                <div class="bookinn-widget-content">
                    <div class="bookinn-departures-list" id="todays-departures">
                        <?php $this->render_todays_departures(); ?>
                    </div>
                </div>
            </div>

            <!-- Notifications -->
            <div class="bookinn-sidebar-widget">
                <div class="bookinn-widget-header">
                    <h4><?php _e('Notifications', 'bookinn'); ?></h4>
                    <span class="bookinn-widget-count" id="notifications-count">0</span>
                </div>
                <div class="bookinn-widget-content">
                    <div class="bookinn-notifications-list" id="notifications-list">
                        <?php $this->render_notifications(); ?>
                    </div>
                </div>
            </div>

            <!-- Room Status Overview -->
            <div class="bookinn-sidebar-widget">
                <div class="bookinn-widget-header">
                    <h4><?php _e('Room Status', 'bookinn'); ?></h4>
                </div>
                <div class="bookinn-widget-content">
                    <div class="bookinn-room-status-overview" id="room-status-overview">
                        <?php $this->render_room_status_overview(); ?>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render today's arrivals
     */
    private function render_todays_arrivals() {
        $today = date('Y-m-d');
        $arrivals = BookInn_Database_Manager::get_results(
            "SELECT b.*, g.first_name, g.last_name, r.room_number
            FROM {bookings} b
            LEFT JOIN {guests} g ON b.guest_id = g.id
            LEFT JOIN {rooms} r ON b.room_id = r.id
            WHERE b.check_in_date = '$today' AND b.status IN ('confirmed', 'pending')
            ORDER BY b.created_at DESC"
        );

        if (empty($arrivals)) {
            echo '<p class="bookinn-no-data">' . __('No arrivals today', 'bookinn') . '</p>';
            return;
        }

        foreach ($arrivals as $arrival) {
            $guest_name = $arrival->first_name . ' ' . $arrival->last_name;
            ?>
            <div class="bookinn-arrival-item">
                <div class="bookinn-guest-info">
                    <strong><?php echo esc_html($guest_name); ?></strong>
                    <small><?php echo esc_html($arrival->room_number); ?></small>
                </div>
                <button class="bookinn-btn-small bookinn-check-in-btn" data-booking-id="<?php echo esc_attr($arrival->id); ?>">
                    <?php _e('Check In', 'bookinn'); ?>
                </button>
            </div>
            <?php
        }
    }

    /**
     * Render today's departures
     */
    private function render_todays_departures() {
        $today = date('Y-m-d');
        $departures = BookInn_Database_Manager::get_results(
            "SELECT b.*, g.first_name, g.last_name, r.room_number
            FROM {bookings} b
            LEFT JOIN {guests} g ON b.guest_id = g.id
            LEFT JOIN {rooms} r ON b.room_id = r.id
            WHERE b.check_out_date = '$today' AND b.status = 'checked_in'
            ORDER BY b.created_at DESC"
        );

        if (empty($departures)) {
            echo '<p class="bookinn-no-data">' . __('No departures today', 'bookinn') . '</p>';
            return;
        }

        foreach ($departures as $departure) {
            $guest_name = $departure->first_name . ' ' . $departure->last_name;
            ?>
            <div class="bookinn-departure-item">
                <div class="bookinn-guest-info">
                    <strong><?php echo esc_html($guest_name); ?></strong>
                    <small><?php echo esc_html($departure->room_number); ?></small>
                </div>
                <button class="bookinn-btn-small bookinn-check-out-btn" data-booking-id="<?php echo esc_attr($departure->id); ?>">
                    <?php _e('Check Out', 'bookinn'); ?>
                </button>
            </div>
            <?php
        }
    }

    /**
     * Render notifications
     */
    private function render_notifications() {
        // Mock notifications - in real implementation, these would come from database
        $notifications = array(
            array(
                'type' => 'warning',
                'message' => __('Room 101 needs maintenance', 'bookinn'),
                'time' => '2 hours ago'
            ),
            array(
                'type' => 'info',
                'message' => __('New booking received', 'bookinn'),
                'time' => '1 hour ago'
            ),
            array(
                'type' => 'success',
                'message' => __('Payment confirmed for booking #123', 'bookinn'),
                'time' => '30 minutes ago'
            )
        );

        if (empty($notifications)) {
            echo '<p class="bookinn-no-data">' . __('No notifications', 'bookinn') . '</p>';
            return;
        }

        foreach ($notifications as $notification) {
            ?>
            <div class="bookinn-notification-item bookinn-notification-<?php echo esc_attr($notification['type']); ?>">
                <div class="bookinn-notification-content">
                    <p><?php echo esc_html($notification['message']); ?></p>
                    <small><?php echo esc_html($notification['time']); ?></small>
                </div>
            </div>
            <?php
        }
    }

    /**
     * Render room status overview
     */
    private function render_room_status_overview() {
        $status_counts = BookInn_Database_Manager::get_results("
            SELECT status, COUNT(*) as count
            FROM {rooms}
            WHERE is_active = 1
            GROUP BY status
        ");

        $statuses = array(
            'available' => array('label' => __('Available', 'bookinn'), 'count' => 0, 'color' => 'green'),
            'occupied' => array('label' => __('Occupied', 'bookinn'), 'count' => 0, 'color' => 'red'),
            'maintenance' => array('label' => __('Maintenance', 'bookinn'), 'count' => 0, 'color' => 'orange'),
            'cleaning' => array('label' => __('Cleaning', 'bookinn'), 'count' => 0, 'color' => 'purple'),
            'out_of_order' => array('label' => __('Out of Order', 'bookinn'), 'count' => 0, 'color' => 'gray')
        );

        foreach ($status_counts as $status) {
            if (isset($statuses[$status->status])) {
                $statuses[$status->status]['count'] = $status->count;
            }
        }

        foreach ($statuses as $key => $status) {
            ?>
            <div class="bookinn-status-item">
                <span class="bookinn-status-indicator bookinn-status-<?php echo esc_attr($key); ?>"></span>
                <span class="bookinn-status-label"><?php echo esc_html($status['label']); ?></span>
                <span class="bookinn-status-count"><?php echo esc_html($status['count']); ?></span>
            </div>
            <?php
        }
    }

    /**
     * Render Gantt chart for the widget (standalone implementation)
     */
    private function render_gantt_chart() {
        // Include CSS directly
        $css_url = plugins_url('assets/css/bookinn-gantt.css', BOOKINN_PLUGIN_FILE);
        echo '<link rel="stylesheet" href="' . esc_url($css_url) . '?ver=1.0" type="text/css" media="all" />';

        // Include JavaScript directly
        $js_url = plugins_url('assets/js/bookinn-gantt.js', BOOKINN_PLUGIN_FILE);
        echo '<script src="' . esc_url($js_url) . '?ver=1.0"></script>';

        // Set up BookInnGantt object
        echo '<script>
        if (typeof BookInnGantt === "undefined") {
            window.BookInnGantt = {};
        }
        BookInnGantt.ajax_url = "' . admin_url('admin-ajax.php') . '";
        BookInnGantt.nonce = "' . wp_create_nonce('bookinn_gantt_widget') . '";
        </script>';

        ob_start();
        ?>
        <div class="gantt-container widget-integrated" id="widgetGanttChart">
            <div class="gantt-chart">
                <table class="gantt-table" id="widgetGanttTable">
                    <thead>
                        <tr id="widgetHeaderRow">
                            <th class="room-header"></th>
                        </tr>
                    </thead>
                    <tbody id="widgetGanttBody">
                        <!-- Rows will be populated via JavaScript -->
                    </tbody>
                </table>
            </div>


        </div>

        <div class="tooltip" id="tooltip"></div>
        <?php
        return ob_get_clean();
    }

    /**
     * Initialize Gantt AJAX handlers
     */
    private function init_gantt_ajax_handlers() {
        add_action('wp_ajax_bookinn_widget_get_rooms_and_bookings', array($this, 'ajax_get_rooms_and_bookings'));
        add_action('wp_ajax_bookinn_widget_gantt_apply_filters', array($this, 'ajax_gantt_apply_filters'));
    // New handler to fetch full booking rows by IDs for accurate dataset
    add_action('wp_ajax_bookinn_widget_get_bookings_by_ids', array($this, 'ajax_get_bookings_by_ids'));
    }

    /**
     * AJAX handler to get rooms and bookings for widget Gantt
     */
    public function ajax_get_rooms_and_bookings() {
    // debug: widget gantt ajax_get_rooms_and_bookings call log suppressed
        check_ajax_referer('bookinn_gantt_widget', 'nonce');

        global $wpdb;

        try {
            $rooms = $wpdb->get_results("SELECT id, name, room_number, status, room_type_id FROM {$wpdb->prefix}bookinn_rooms", ARRAY_A);
            $bookings = $wpdb->get_results("
                SELECT b.id, b.room_id as roomId, b.guest_id, g.first_name, g.last_name,
                       b.check_in_date as checkIn, b.check_out_date as checkOut, b.status,
                       b.adults, b.children, b.total_amount as totalAmount
                FROM {$wpdb->prefix}bookinn_bookings b
                LEFT JOIN {$wpdb->prefix}bookinn_guests g ON b.guest_id = g.id
            ", ARRAY_A);

            // Process bookings data
            foreach ($bookings as &$b) {
                $b['guestName'] = trim(($b['first_name'] ?? '') . ' ' . ($b['last_name'] ?? ''));
                $b['guests'] = intval($b['adults'] ?? 1) + intval($b['children'] ?? 0);
            }
            unset($b);

            wp_send_json_success(['rooms' => $rooms, 'bookings' => $bookings]);

        } catch (Exception $e) {
            error_log('BookInn Widget Gantt Error: ' . $e->getMessage());
            wp_send_json_error('Failed to load Gantt data: ' . $e->getMessage());
        }
    }

    /**
     * AJAX handler: return full booking DB rows for given booking IDs
     */
    public function ajax_get_bookings_by_ids() {
        check_ajax_referer('bookinn_gantt_widget', 'nonce');
        global $wpdb;
        try {
            $ids = isset($_POST['ids']) ? (array) $_POST['ids'] : [];
            if (empty($ids)) {
                wp_send_json_success(['bookings' => []]);
            }
            // Sanitize and prepare placeholders
            $placeholders = implode(',', array_fill(0, count($ids), '%d'));
            $query = $wpdb->prepare("SELECT b.* , g.first_name AS guest_first_name, g.last_name AS guest_last_name, g.email AS guest_email, g.phone AS guest_phone FROM {$wpdb->prefix}bookinn_bookings b LEFT JOIN {$wpdb->prefix}bookinn_guests g ON b.guest_id = g.id WHERE b.id IN ($placeholders)", $ids);
            $rows = $wpdb->get_results($query, ARRAY_A);
            wp_send_json_success(['bookings' => $rows]);
        } catch (Exception $e) {
            error_log('BookInn AJAX get_bookings_by_ids error: ' . $e->getMessage());
            wp_send_json_error('Failed to fetch booking rows');
        }
    }

    /**
     * AJAX handler to apply filters to widget Gantt
     */
    public function ajax_gantt_apply_filters() {
        check_ajax_referer('bookinn_gantt_widget', 'nonce');

        $view_mode = sanitize_text_field($_POST['viewMode'] ?? 'month');
        $current_date = sanitize_text_field($_POST['currentDate'] ?? date('Y-m-d'));
        $filters = $_POST['filters'] ?? array();

        // Sanitize filters
        $room_type = sanitize_text_field($filters['roomType'] ?? '');
        $booking_status = sanitize_text_field($filters['bookingStatus'] ?? '');
        $room_status = sanitize_text_field($filters['roomStatus'] ?? '');
        $custom_start = sanitize_text_field($filters['customStart'] ?? '');
        $custom_end = sanitize_text_field($filters['customEnd'] ?? '');

        global $wpdb;

        try {
            // Build room query with filters
            $room_where = "1=1";
            $room_params = array();

            if (!empty($room_type)) {
                $room_where .= " AND room_type_id = %s";
                $room_params[] = $room_type;
            }

            if (!empty($room_status)) {
                $room_where .= " AND status = %s";
                $room_params[] = $room_status;
            }

            $rooms_query = "SELECT id, name, room_number, status, room_type_id FROM {$wpdb->prefix}bookinn_rooms WHERE {$room_where}";
            if (!empty($room_params)) {
                $rooms = $wpdb->get_results($wpdb->prepare($rooms_query, $room_params), ARRAY_A);
            } else {
                $rooms = $wpdb->get_results($rooms_query, ARRAY_A);
            }

            // Build booking query with filters
            $booking_where = "1=1";
            $booking_params = array();

            if (!empty($booking_status)) {
                $booking_where .= " AND b.status = %s";
                $booking_params[] = $booking_status;
            }

            // Date range filters
            if (!empty($custom_start)) {
                $booking_where .= " AND b.check_out_date >= %s";
                $booking_params[] = $custom_start;
            }

            if (!empty($custom_end)) {
                $booking_where .= " AND b.check_in_date <= %s";
                $booking_params[] = $custom_end;
            }

            // Return full DB row for bookings so frontend can render authoritative data in a single request
            $bookings_query = "
                SELECT b.*, g.first_name AS guest_first_name, g.last_name AS guest_last_name, g.email AS guest_email, g.phone AS guest_phone
                FROM {$wpdb->prefix}bookinn_bookings b
                LEFT JOIN {$wpdb->prefix}bookinn_guests g ON b.guest_id = g.id
                WHERE {$booking_where}
            ";

            if (!empty($booking_params)) {
                $bookings = $wpdb->get_results($wpdb->prepare($bookings_query, $booking_params), ARRAY_A);
            } else {
                $bookings = $wpdb->get_results($bookings_query, ARRAY_A);
            }

            // Process bookings data
            foreach ($bookings as &$b) {
                $b['guestName'] = trim(($b['first_name'] ?? '') . ' ' . ($b['last_name'] ?? ''));
                $b['guests'] = intval($b['adults'] ?? 1) + intval($b['children'] ?? 0);
            }
            unset($b);

            // Return filtered data
            wp_send_json_success(array(
                'rooms' => $rooms,
                'bookings' => $bookings,
                'applied_filters' => $filters,
                'view_mode' => $view_mode,
                'current_date' => $current_date
            ));

        } catch (Exception $e) {
            error_log('BookInn Widget Gantt Apply Filters Error: ' . $e->getMessage());
            wp_send_json_error('Failed to apply filters: ' . $e->getMessage());
        }
    }

    /**
     * Load Rate Plans Manager
     */
    private function load_rate_plans_manager() {
        if (!class_exists('BookInn_Rate_Plans_Manager')) {
            require_once BOOKINN_PLUGIN_PATH . 'includes/class-bookinn-rate-plans-manager.php';
        }
    }

    /**
     * Load Rate Plans Migration
     */
    private function load_rate_plans_migration() {
        if (!class_exists('BookInn_Rate_Plans_Migration')) {
            require_once BOOKINN_PLUGIN_PATH . 'includes/class-bookinn-rate-plans-migration.php';
        }

        // Run migrations if needed
        BookInn_Rate_Plans_Migration::run_migrations();
    }

    /**
     * Load Guest Registry Manager
     */
    private function load_guest_registry_manager() {
        if (!class_exists('BookInn_Guest_Registry_Manager')) {
            require_once BOOKINN_PLUGIN_PATH . 'includes/class-bookinn-guest-registry-manager.php';
        }
    }

    /**
     * Load Booking Enhancements
     */
    private function load_booking_enhancements() {
        // Load Booking Enhancements Migration
        if (!class_exists('BookInn_Booking_Enhancements_Migration')) {
            require_once BOOKINN_PLUGIN_PATH . 'includes/class-bookinn-booking-enhancements-migration.php';
        }

        // Load Enhanced Booking Manager
        if (!class_exists('BookInn_Booking_Enhanced_Manager')) {
            require_once BOOKINN_PLUGIN_PATH . 'includes/class-bookinn-booking-enhanced-manager.php';
        }

        // Run migrations if needed
        BookInn_Booking_Enhancements_Migration::run_migrations();
    }
}
