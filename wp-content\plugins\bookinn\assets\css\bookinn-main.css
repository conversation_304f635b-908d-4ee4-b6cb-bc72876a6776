/*
 * BookInn Main Stylesheet
 * VikBooking-inspired professional design system
 */

/* CSS Variables - Design System */
:root {
    /* Primary Colors  */
    --bookinn-primary: #4A8DDC;
    --bookinn-primary-dark: #1d4ed8;
    --bookinn-primary-light: #3b82f6;
    --bookinn-secondary: #10b981;
    --bookinn-secondary-dark: #059669;
    --bookinn-secondary-light: #34d399;
    
    /* Status Colors */
    --bookinn-success: #10b981;
    --bookinn-warning: #f59e0b;
    --bookinn-error: #ef4444;
    --bookinn-info: #3b82f6;
    
    /* Neutral Colors */
    --bookinn-gray-50: #f9fafb;
    --bookinn-gray-100: #f3f4f6;
    --bookinn-gray-200: #e5e7eb;
    --bookinn-gray-300: #d1d5db;
    --bookinn-gray-400: #9ca3af;
    --bookinn-gray-500: #6b7280;
    --bookinn-gray-600: #4b5563;
    --bookinn-gray-700: #374151;
    --bookinn-gray-800: #1f2937;
    --bookinn-gray-900: #111827;
    
    /* Background Colors */
    --bookinn-bg-primary: #ffffff;
    --bookinn-bg-secondary: #f9fafb;
    --bookinn-bg-tertiary: #f3f4f6;
    --bookinn-bg-dark: #1f2937;
    
    /* Border Colors */
    --bookinn-border-light: #e5e7eb;
    --bookinn-border-medium: #d1d5db;
    --bookinn-border-dark: #9ca3af;
    
    /* Text Colors */
    --bookinn-text-primary: #111827;
    --bookinn-text-secondary: #4b5563;
    --bookinn-text-muted: #6b7280;
    --bookinn-text-inverse: #ffffff;
    
    /* Shadows */
    --bookinn-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --bookinn-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --bookinn-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --bookinn-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --bookinn-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Typography */
    --bookinn-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --bookinn-font-mono: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
    
    /* Font Sizes */
    --bookinn-text-xs: 0.75rem; /* 12px */
    --bookinn-text-sm: 0.875rem; /* 14px */
    --bookinn-text-base: 1rem; /* 16px */
    --bookinn-text-lg: 1.125rem; /* 18px */
    --bookinn-text-xl: 1.25rem; /* 20px */
    --bookinn-text-2xl: 1.5rem; /* 24px */
    --bookinn-text-3xl: 1.875rem; /* 30px */
    --bookinn-text-4xl: 2.25rem; /* 36px */
    
    /* Line Heights */
    --bookinn-leading-tight: 1.25;
    --bookinn-leading-snug: 1.375;
    --bookinn-leading-normal: 1.5;
    --bookinn-leading-relaxed: 1.625;
    
    /* Spacing */
    --bookinn-space-1: 0.25rem; /* 4px */
    --bookinn-space-2: 0.5rem; /* 8px */
    --bookinn-space-3: 0.75rem; /* 12px */
    --bookinn-space-4: 1rem; /* 16px */
    --bookinn-space-5: 1.25rem; /* 20px */
    --bookinn-space-6: 1.5rem; /* 24px */
    --bookinn-space-8: 2rem; /* 32px */
    --bookinn-space-10: 2.5rem; /* 40px */
    --bookinn-space-12: 3rem; /* 48px */
    --bookinn-space-16: 4rem; /* 64px */
    --bookinn-space-20: 5rem; /* 80px */
    
    /* Border Radius */
    --bookinn-radius-sm: 0.125rem; /* 2px */
    --bookinn-radius: 0.25rem; /* 4px */
    --bookinn-radius-md: 0.375rem; /* 6px */
    --bookinn-radius-lg: 0.5rem; /* 8px */
    --bookinn-radius-xl: 0.75rem; /* 12px */
    --bookinn-radius-2xl: 1rem; /* 16px */
    --bookinn-radius-full: 9999px;
    
    /* Transitions */
    --bookinn-transition: all 0.15s ease-in-out;
    --bookinn-transition-fast: all 0.1s ease-in-out;
    --bookinn-transition-slow: all 0.3s ease-in-out;
    
    /* Z-index scale */
    --bookinn-z-dropdown: 1000;
    --bookinn-z-sticky: 1020;
    --bookinn-z-fixed: 1030;
    --bookinn-z-modal-backdrop: 1040;
    --bookinn-z-modal: 1050;
    --bookinn-z-popover: 1060;
    --bookinn-z-tooltip: 1070;
    
    /* Breakpoints */
    --bookinn-breakpoint-sm: 640px;
    --bookinn-breakpoint-md: 768px;
    --bookinn-breakpoint-lg: 1024px;
    --bookinn-breakpoint-xl: 1280px;
    --bookinn-breakpoint-2xl: 1536px;
}

/* Reset and Base Styles */
.bookinn-widget *,
.bookinn-widget *::before,
.bookinn-widget *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

.bookinn-widget {
    font-family: var(--bookinn-font-family);
    font-size: var(--bookinn-text-base);
    line-height: var(--bookinn-leading-normal);
    color: var(--bookinn-text-primary);
    background-color: var(--bookinn-bg-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
.bookinn-widget h1,
.bookinn-widget h2,
.bookinn-widget h3,
.bookinn-widget h4,
.bookinn-widget h5,
.bookinn-widget h6 {
    font-weight: 600;
    line-height: var(--bookinn-leading-tight);
    margin-bottom: var(--bookinn-space-4);
}

.bookinn-widget h1 { font-size: var(--bookinn-text-3xl); }
.bookinn-widget h2 { font-size: var(--bookinn-text-2xl); }
.bookinn-widget h3 { font-size: var(--bookinn-text-xl); }
.bookinn-widget h4 { font-size: var(--bookinn-text-lg); }
.bookinn-widget h5 { font-size: var(--bookinn-text-base); }
.bookinn-widget h6 { font-size: var(--bookinn-text-sm); }

.bookinn-widget p {
    margin-bottom: var(--bookinn-space-4);
    line-height: var(--bookinn-leading-relaxed);
}

.bookinn-widget a {
    color: var(--bookinn-primary);
    text-decoration: none;
    transition: var(--bookinn-transition);
}

.bookinn-widget a:hover {
    color: var(--bookinn-primary-dark);
    text-decoration: underline;
}

/* Utility Classes */
.bookinn-text-xs { font-size: var(--bookinn-text-xs); }
.bookinn-text-sm { font-size: var(--bookinn-text-sm); }
.bookinn-text-base { font-size: var(--bookinn-text-base); }
.bookinn-text-lg { font-size: var(--bookinn-text-lg); }
.bookinn-text-xl { font-size: var(--bookinn-text-xl); }
.bookinn-text-2xl { font-size: var(--bookinn-text-2xl); }

.bookinn-text-primary { color: var(--bookinn-text-primary); }
.bookinn-text-secondary { color: var(--bookinn-text-secondary); }
.bookinn-text-muted { color: var(--bookinn-text-muted); }
.bookinn-text-inverse { color: var(--bookinn-text-inverse); }

.bookinn-font-medium { font-weight: 500; }
.bookinn-font-semibold { font-weight: 600; }
.bookinn-font-bold { font-weight: 700; }

.bookinn-text-center { text-align: center; }
.bookinn-text-left { text-align: left; }
.bookinn-text-right { text-align: right; }

/* Layout Utilities */
.bookinn-flex { display: flex; }
.bookinn-inline-flex { display: inline-flex; }
.bookinn-grid { display: grid; }
.bookinn-block { display: block; }
.bookinn-inline-block { display: inline-block; }
.bookinn-hidden { display: none; }

.bookinn-flex-col { flex-direction: column; }
.bookinn-flex-row { flex-direction: row; }
.bookinn-items-center { align-items: center; }
.bookinn-items-start { align-items: flex-start; }
.bookinn-items-end { align-items: flex-end; }
.bookinn-justify-center { justify-content: center; }
.bookinn-justify-between { justify-content: space-between; }
.bookinn-justify-start { justify-content: flex-start; }
.bookinn-justify-end { justify-content: flex-end; }

.bookinn-flex-1 { flex: 1 1 0%; }
.bookinn-flex-auto { flex: 1 1 auto; }
.bookinn-flex-none { flex: none; }

.bookinn-gap-1 { gap: var(--bookinn-space-1); }
.bookinn-gap-2 { gap: var(--bookinn-space-2); }
.bookinn-gap-3 { gap: var(--bookinn-space-3); }
.bookinn-gap-4 { gap: var(--bookinn-space-4); }
.bookinn-gap-6 { gap: var(--bookinn-space-6); }
.bookinn-gap-8 { gap: var(--bookinn-space-8); }

/* Spacing Utilities */
.bookinn-p-0 { padding: 0; }
.bookinn-p-1 { padding: var(--bookinn-space-1); }
.bookinn-p-2 { padding: var(--bookinn-space-2); }
.bookinn-p-3 { padding: var(--bookinn-space-3); }
.bookinn-p-4 { padding: var(--bookinn-space-4); }
.bookinn-p-6 { padding: var(--bookinn-space-6); }
.bookinn-p-8 { padding: var(--bookinn-space-8); }

.bookinn-px-0 { padding-left: 0; padding-right: 0; }
.bookinn-px-1 { padding-left: var(--bookinn-space-1); padding-right: var(--bookinn-space-1); }
.bookinn-px-2 { padding-left: var(--bookinn-space-2); padding-right: var(--bookinn-space-2); }
.bookinn-px-3 { padding-left: var(--bookinn-space-3); padding-right: var(--bookinn-space-3); }
.bookinn-px-4 { padding-left: var(--bookinn-space-4); padding-right: var(--bookinn-space-4); }
.bookinn-px-6 { padding-left: var(--bookinn-space-6); padding-right: var(--bookinn-space-6); }

.bookinn-py-0 { padding-top: 0; padding-bottom: 0; }
.bookinn-py-1 { padding-top: var(--bookinn-space-1); padding-bottom: var(--bookinn-space-1); }
.bookinn-py-2 { padding-top: var(--bookinn-space-2); padding-bottom: var(--bookinn-space-2); }
.bookinn-py-3 { padding-top: var(--bookinn-space-3); padding-bottom: var(--bookinn-space-3); }
.bookinn-py-4 { padding-top: var(--bookinn-space-4); padding-bottom: var(--bookinn-space-4); }
.bookinn-py-6 { padding-top: var(--bookinn-space-6); padding-bottom: var(--bookinn-space-6); }

.bookinn-m-0 { margin: 0; }
.bookinn-m-1 { margin: var(--bookinn-space-1); }
.bookinn-m-2 { margin: var(--bookinn-space-2); }
.bookinn-m-3 { margin: var(--bookinn-space-3); }
.bookinn-m-4 { margin: var(--bookinn-space-4); }
.bookinn-m-6 { margin: var(--bookinn-space-6); }
.bookinn-m-8 { margin: var(--bookinn-space-8); }

.bookinn-mx-auto { margin-left: auto; margin-right: auto; }
.bookinn-mb-0 { margin-bottom: 0; }
.bookinn-mb-1 { margin-bottom: var(--bookinn-space-1); }
.bookinn-mb-2 { margin-bottom: var(--bookinn-space-2); }
.bookinn-mb-3 { margin-bottom: var(--bookinn-space-3); }
.bookinn-mb-4 { margin-bottom: var(--bookinn-space-4); }
.bookinn-mb-6 { margin-bottom: var(--bookinn-space-6); }

/* Width and Height */
.bookinn-w-full { width: 100%; }
.bookinn-w-auto { width: auto; }
.bookinn-h-full { height: 100%; }
.bookinn-h-auto { height: auto; }

/* Border Utilities */
.bookinn-border { border: 1px solid var(--bookinn-border-light); }
.bookinn-border-0 { border: 0; }
.bookinn-border-t { border-top: 1px solid var(--bookinn-border-light); }
.bookinn-border-b { border-bottom: 1px solid var(--bookinn-border-light); }
.bookinn-border-l { border-left: 1px solid var(--bookinn-border-light); }
.bookinn-border-r { border-right: 1px solid var(--bookinn-border-light); }

.bookinn-rounded { border-radius: var(--bookinn-radius); }
.bookinn-rounded-sm { border-radius: var(--bookinn-radius-sm); }
.bookinn-rounded-md { border-radius: var(--bookinn-radius-md); }
.bookinn-rounded-lg { border-radius: var(--bookinn-radius-lg); }
.bookinn-rounded-xl { border-radius: var(--bookinn-radius-xl); }
.bookinn-rounded-full { border-radius: var(--bookinn-radius-full); }

/* Shadow Utilities */
.bookinn-shadow-none { box-shadow: none; }
.bookinn-shadow-sm { box-shadow: var(--bookinn-shadow-sm); }
.bookinn-shadow { box-shadow: var(--bookinn-shadow); }
.bookinn-shadow-md { box-shadow: var(--bookinn-shadow-md); }
.bookinn-shadow-lg { box-shadow: var(--bookinn-shadow-lg); }
.bookinn-shadow-xl { box-shadow: var(--bookinn-shadow-xl); }

/* Background Utilities */
.bookinn-bg-primary { background-color: var(--bookinn-bg-primary); }
.bookinn-bg-secondary { background-color: var(--bookinn-bg-secondary); }
.bookinn-bg-tertiary { background-color: var(--bookinn-bg-tertiary); }

.bookinn-bg-blue-50 { background-color: #eff6ff; }
.bookinn-bg-blue-100 { background-color: #dbeafe; }
.bookinn-bg-blue-500 { background-color: var(--bookinn-primary); }
.bookinn-bg-blue-600 { background-color: var(--bookinn-primary-dark); }

.bookinn-bg-green-50 { background-color: #ecfdf5; }
.bookinn-bg-green-100 { background-color: #d1fae5; }
.bookinn-bg-green-500 { background-color: var(--bookinn-secondary); }
.bookinn-bg-green-600 { background-color: var(--bookinn-secondary-dark); }

.bookinn-bg-red-50 { background-color: #fef2f2; }
.bookinn-bg-red-100 { background-color: #fee2e2; }
.bookinn-bg-red-500 { background-color: var(--bookinn-error); }

.bookinn-bg-yellow-50 { background-color: #fffbeb; }
.bookinn-bg-yellow-100 { background-color: #fef3c7; }
.bookinn-bg-yellow-500 { background-color: var(--bookinn-warning); }

/* Position Utilities */
.bookinn-relative { position: relative; }
.bookinn-absolute { position: absolute; }
.bookinn-fixed { position: fixed; }
.bookinn-sticky { position: sticky; }

.bookinn-top-0 { top: 0; }
.bookinn-right-0 { right: 0; }
.bookinn-bottom-0 { bottom: 0; }
.bookinn-left-0 { left: 0; }

/* Overflow Utilities */
.bookinn-overflow-hidden { overflow: hidden; }
.bookinn-overflow-auto { overflow: auto; }
.bookinn-overflow-scroll { overflow: scroll; }

.bookinn-overflow-x-hidden { overflow-x: hidden; }
.bookinn-overflow-x-auto { overflow-x: auto; }
.bookinn-overflow-y-hidden { overflow-y: hidden; }
.bookinn-overflow-y-auto { overflow-y: auto; }

/* Z-index Utilities */
.bookinn-z-0 { z-index: 0; }
.bookinn-z-10 { z-index: 10; }
.bookinn-z-20 { z-index: 20; }
.bookinn-z-30 { z-index: 30; }
.bookinn-z-40 { z-index: 40; }
.bookinn-z-50 { z-index: 50; }

/* Opacity Utilities */
.bookinn-opacity-0 { opacity: 0; }
.bookinn-opacity-25 { opacity: 0.25; }
.bookinn-opacity-50 { opacity: 0.5; }
.bookinn-opacity-75 { opacity: 0.75; }
.bookinn-opacity-100 { opacity: 1; }

/* Transition Utilities */
.bookinn-transition { transition: var(--bookinn-transition); }
.bookinn-transition-fast { transition: var(--bookinn-transition-fast); }
.bookinn-transition-slow { transition: var(--bookinn-transition-slow); }

/* Transform Utilities */
.bookinn-transform { transform: translateZ(0); }
.bookinn-scale-95 { transform: scale(0.95); }
.bookinn-scale-100 { transform: scale(1); }
.bookinn-scale-105 { transform: scale(1.05); }

/* Cursor Utilities */
.bookinn-cursor-pointer { cursor: pointer; }
.bookinn-cursor-default { cursor: default; }
.bookinn-cursor-not-allowed { cursor: not-allowed; }

/* Select Utilities */
.bookinn-select-none { user-select: none; }
.bookinn-select-text { user-select: text; }
.bookinn-select-all { user-select: all; }

/* Screen Reader Only */
.bookinn-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Animation keyframes */
@keyframes bookinn-spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes bookinn-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes bookinn-bounce {
    0%, 100% {
        transform: translateY(-25%);
        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    50% {
        transform: translateY(0);
        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
}

@keyframes bookinn-fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes bookinn-slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bookinn-slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animation Utilities */
.bookinn-animate-spin { animation: bookinn-spin 1s linear infinite; }
.bookinn-animate-pulse { animation: bookinn-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.bookinn-animate-bounce { animation: bookinn-bounce 1s infinite; }
.bookinn-animate-fadeIn { animation: bookinn-fadeIn 0.3s ease-in-out; }
.bookinn-animate-slideInUp { animation: bookinn-slideInUp 0.3s ease-out; }
.bookinn-animate-slideInDown { animation: bookinn-slideInDown 0.3s ease-out; }

/* Focus States */
.bookinn-widget *:focus {
    outline: 2px solid var(--bookinn-primary);
    outline-offset: 2px;
}

.bookinn-widget *:focus:not(:focus-visible) {
    outline: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .bookinn-widget {
        font-size: var(--bookinn-text-sm);
    }
    
    .bookinn-md\:hidden {
        display: none;
    }
    
    .bookinn-md\:block {
        display: block;
    }
    
    .bookinn-md\:flex {
        display: flex;
    }
}

@media (min-width: 768px) {
    .bookinn-md\:block {
        display: block;
    }
    
    .bookinn-md\:flex {
        display: flex;
    }
    
    .bookinn-md\:hidden {
        display: none;
    }
    
    .bookinn-md\:px-6 {
        padding-left: var(--bookinn-space-6);
        padding-right: var(--bookinn-space-6);
    }
    
    .bookinn-md\:py-8 {
        padding-top: var(--bookinn-space-8);
        padding-bottom: var(--bookinn-space-8);
    }
}

@media (min-width: 1024px) {
    .bookinn-lg\:px-8 {
        padding-left: var(--bookinn-space-8);
        padding-right: var(--bookinn-space-8);
    }
    
    .bookinn-lg\:py-12 {
        padding-top: var(--bookinn-space-12);
        padding-bottom: var(--bookinn-space-12);
    }
}
